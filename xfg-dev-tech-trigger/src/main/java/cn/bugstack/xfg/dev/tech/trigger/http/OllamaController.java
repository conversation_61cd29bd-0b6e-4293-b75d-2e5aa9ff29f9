package cn.bugstack.xfg.dev.tech.trigger.http;

import cn.bugstack.xfg.dev.tech.api.IAiService;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/28
 */
@RestController
@RequestMapping("/api/v1/ollama/")
@CrossOrigin
@Slf4j
public class OllamaController implements IAiService {

    @Resource
    public ChatClient ollamaChatClient;


    @Override
    @GetMapping("generate")
    public ChatResponse generate(String model, String message) {
        return ollamaChatClient.prompt()
                .user(message)
                .options(OllamaOptions.builder().model(model).build())
                .call()
                .chatResponse();
    }

    @Override
    @GetMapping("generate_stream")
    public Flux<ChatResponse> generateStream(String model, String message) {
        return ollamaChatClient.prompt()
                .user(message)
                .options(OllamaOptions.builder().model(model).build())
                .stream()
                .chatResponse();
    }


}
