package cn.bugstack.xfg.dev.tech.trigger.http;

import cn.bugstack.xfg.dev.tech.api.IRagTagService;
import cn.bugstack.xfg.dev.tech.api.response.ApiResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/29
 */
@Slf4j
@RestController
@CrossOrigin
@RequestMapping("api/v1/ragTag")
public class RagTagController implements IRagTagService {

    @Resource
    private TokenTextSplitter tokenTextSplitter;
    @Resource
    private ChatClient ollamaChatClient;
    @Resource
    private SimpleVectorStore ollamaSimpleVectorStore;
    @Resource
    private PgVectorStore ollamaPgVectorStore;
    @Resource
    private RedissonClient redissonClient;



    @Override
    @GetMapping("query_rag_tag_list")
    public ApiResponse<List<String>> queryRagTagList() {
        RList<String> ragTagList = redissonClient.getList("ragTag");
        return ApiResponse.<List<String>>builder()
                .code("0000")
                .message("调用成功")
                .data(ragTagList)
                .build();
    }

    @Override
    @PostMapping(value = "file/upload", headers = "content-type=multipart/form-data")
    public ApiResponse<String> uploadRagTag(@RequestParam String ragTag, List<MultipartFile> files) {
        log.info("上传知识库开始-ragTag:{}", ragTag);
        files.forEach(file -> {
            TikaDocumentReader documentReader = new TikaDocumentReader(file.getResource());
            List<Document> splitDocument = tokenTextSplitter.apply(documentReader.get());
            splitDocument.forEach(doc -> doc.getMetadata().put("knowledge", ragTag));
            ollamaPgVectorStore.accept(splitDocument);
        });

        RList<Object> ragTagList = redissonClient.getList("ragTag");
        if (!ragTagList.contains(ragTag)){
            ragTagList.add(ragTag);
        }
        log.info("上传知识库完成-ragTag:{}", ragTag);
        return ApiResponse.<String>builder().code("0000").message("调用成功").build();
    }
}
