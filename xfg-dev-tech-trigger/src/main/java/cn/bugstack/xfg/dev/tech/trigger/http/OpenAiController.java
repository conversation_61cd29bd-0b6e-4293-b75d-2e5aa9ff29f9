package cn.bugstack.xfg.dev.tech.trigger.http;

import cn.bugstack.xfg.dev.tech.api.IAiService;
import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/29
 */
@RestController
@RequestMapping("/api/v1/openai/")
@CrossOrigin
@Slf4j
public class OpenAiController implements IAiService {

    @Resource
    public ChatClient openAiChatClient;

    @Resource
    private PgVectorStore openAiPgVectorStore;


    @Override
    @GetMapping("generate")
    public ChatResponse generate(String model, String message) {
        return openAiChatClient.prompt()
                .user(message)
                .options(OpenAiChatOptions.builder().model(model).build())
                .call()
                .chatResponse();
    }

    @Override
    @GetMapping("generate_stream")
    public Flux<ChatResponse> generateStream(String model, String message) {
        return openAiChatClient.prompt()
                .user(message)
                .options(OpenAiChatOptions.builder().model(model).build())
                .stream()
                .chatResponse();
    }

    @GetMapping("generate_stream_rag")
    public Flux<ChatResponse> generateStreamRag(@RequestParam String model, @RequestParam String message) {
        String prompt = "用户想了解 {message}。请先提炼问题的核心意思，然后结合以下知识库中的内容，给出最清晰、最准确的答案。DOCUMENTS:{documents}";
        SearchRequest searchRequest = SearchRequest.builder()
                .query(message)
                .topK(5)
                .build();

        List<Document> documents = openAiPgVectorStore.similaritySearch(searchRequest);
        String document;
        if (documents != null) {
            document = documents.stream()
                    .map(Document::getFormattedContent)
                    .collect(Collectors.joining());
        } else {
            document = "暂无相关知识";
        }

        Message finalMessage = new SystemPromptTemplate(prompt).createMessage(Map.of("message", message, "documents", document));
        log.info("finalPrompt：{}", JSON.toJSONString(finalMessage));

        return openAiChatClient.prompt()
                .messages(finalMessage)
                .options(OpenAiChatOptions.builder()
                        .model(model)
                        .build())
                .stream()
                .chatResponse();
    }
}
