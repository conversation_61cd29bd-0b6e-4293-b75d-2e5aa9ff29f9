25-07-30.09:14:09.239 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 14420 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-30.09:14:09.241 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-30.09:14:10.787 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-30.09:14:10.794 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-30.09:14:10.852 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
25-07-30.09:14:12.283 [main            ] INFO  TomcatWebServer        - Tom<PERSON> initialized with port 8080 (http)
25-07-30.09:14:12.310 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-30.09:14:12.311 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-30.09:14:12.311 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-07-30.09:14:12.648 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-30.09:14:12.648 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3060 ms
25-07-30.09:14:13.657 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-07-30.09:14:13.675 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-07-30.09:14:13.675 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:14:14.584 [main            ] INFO  Version                - Redisson 3.44.0
25-07-30.09:14:15.056 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:14:15.073 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:14:15.261 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama. Is empty: false
25-07-30.09:14:15.261 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama in schema: public
25-07-30.09:14:15.261 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:14:16.204 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-30.09:14:16.219 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path '/'
25-07-30.09:14:16.229 [main            ] INFO  Application            - Started Application in 7.966 seconds (process running for 9.715)
25-07-30.09:14:42.151 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-07-30.09:14:42.166 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-07-30.09:18:50.641 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 8760 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-30.09:18:50.643 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-30.09:18:51.997 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-30.09:18:52.004 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-30.09:18:52.058 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
25-07-30.09:18:53.263 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-30.09:18:53.286 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-30.09:18:53.288 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-30.09:18:53.288 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-07-30.09:18:53.584 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-30.09:18:53.585 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2648 ms
25-07-30.09:18:54.373 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-07-30.09:18:54.380 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-07-30.09:18:54.380 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:18:55.131 [main            ] INFO  Version                - Redisson 3.44.0
25-07-30.09:18:55.536 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:18:55.557 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:18:55.723 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama. Is empty: false
25-07-30.09:18:55.725 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama in schema: public
25-07-30.09:18:55.725 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:18:56.539 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-30.09:18:56.555 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path '/'
25-07-30.09:18:56.567 [main            ] INFO  Application            - Started Application in 6.754 seconds (process running for 8.142)
25-07-30.09:19:39.626 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-30.09:19:39.626 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-30.09:19:39.628 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 2 ms
25-07-30.09:19:39.731 [http-nio-8080-exec-1] INFO  RagTagController       - 上传知识库开始-ragTag:《凡人修仙传》设定
25-07-30.09:19:40.767 [http-nio-8080-exec-1] INFO  TextSplitter           - Splitting up document into 3 chunks.
25-07-30.09:19:41.741 [http-nio-8080-exec-1] INFO  HikariDataSource       - HikariCP - Starting...
25-07-30.09:19:41.929 [http-nio-8080-exec-1] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@5ff0f479
25-07-30.09:19:41.931 [http-nio-8080-exec-1] INFO  HikariDataSource       - HikariCP - Start completed.
25-07-30.09:19:42.014 [http-nio-8080-exec-1] ERROR [dispatcherServlet]    - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.dao.DataIntegrityViolationException: PreparedStatementCallback; SQL [INSERT INTO public.vector_store_openai (id, content, metadata, embedding) VALUES (?, ?, ?::jsonb, ?) ON CONFLICT (id) DO UPDATE SET content = ? , metadata = ?::jsonb , embedding = ? ]; Batch entry 0 INSERT INTO public.vector_store_openai (id, content, metadata, embedding) VALUES (('c4a2c6ab-8203-494b-af5c-e73b37ae673e'::uuid), ('1.没有“灵根”真的就无法修仙吗?
在凡人的世界中，“灵根”与修仙是画上等号的，但事无绝对，在灵界有天材地宝可以凭空生出“灵根”来，灵界的一位人族大能就是用灵宝获得“灵根”成为的修士。但在人界是没有这种灵草的，所以可以说人界没有“灵根”就无法修仙。后面剧情中师妹与韩立关于“灵根”的一番话也是凡人中的一段金典，让人唏嘘不已。

2.“灵根”有几种?韩立的“灵根”怎么样?
灵根普遍上可分为五种，“灵根”分为金、木、水、火、土等五行属性，大部分人的灵根，都是这五种或四种多重属性混杂，这些人虽然也可以感应到天地灵气，但是修炼的效果可以说是惨不忍睹，基本上只能把炼气阶段的五行基本功法，练至三、四层，就寸步不前了，一般一生都无望跨过筑基期。所以具有五种、甚至四种属性的灵根，也被修仙界称为“伪灵根”，以示和只有两、三种属性，修炼起来较为快速的“真灵根”相区别。 至于只有一种属性的单一灵根，则被修仙界称为“天根”，意思是上天的宠儿。因为拥有这种灵根、不论是何属性的人，他修炼的速度都是普通灵根人的二至三倍。而且修炼到筑基期顶峰时，不需面对跨入结丹期时所应面对的瓶颈，可轻易的开始结丹。而在五种基本属性的“灵根”之外还有变异“灵根”的存在，像“金灵根”和“水灵根”异变产生的“雷灵根”；“土灵根”和“水灵根”异变后产生的“冰灵根”，当然还有“暗灵根”、“风灵根”等其他变异灵根。变异“灵根”没有能直接突破结丹期的妙用的，但在修炼速度上与“天灵根”相差无几，所以同样是门派拉拢的对象。而在灵界之中更是有“隐灵根”的存在，其表现在灵根会在'), ('{"source":"新建 文本文档.txt","knowledge":"《凡人修仙传》设定"}')::jsonb, ('[0.026403623,-0.06667741,0.03509999,-4.7717983E-4,0.028652413,0.02802338,-0.015466327,0.075106435,0.047869343,0.0568016,0.002726461,0.02479959,-0.024736688,-0.04481854,0.062840305,0.008090925,0.006663809,-0.052366924,0.025979027,-0.037238702,0.040509667,-0.032049187,0.05126612,0.05343628,-0.021780238,0.002978074,0.023557253,0.08051611,-0.019940319,-0.0013878021,-0.05934918,0.014632859,-0.023148382,-0.0027952616,-1.2961501E-4,0.007336087,-0.0053860876,0.017754432,0.07208708,0.026136285,0.004937902,-0.038308054,0.013988102,5.2337436E-4,-0.043183055,0.008460482,0.0025711688,-4.3073957E-4,-0.010465522,-0.011794352,0.03874838,0.0095298365,-0.02461088,0.0017878272,0.02027056,0.025900397,0.008381853,0.0146878995,-0.004536894,0.01238407,0.004725604,0.007693849,0.009679232,0.012391932,-0.019437093,-0.014310481,-0.05148628,0.009805039,0.019499995,-0.055763695,-0.051140312,-0.005905039,-0.0021446063,-0.0035756545,0.028463703,-0.021795962,0.018603625,0.014145359,-0.052178215,-0.0033338703,-0.010795764,-0.009938708,0.0085627,-0.029501606,0.015324795,0.057021763,0.010701409,0.010842941,0.020915318,0.10831933,0.0121324565,-0.022362093,0.023730237,-0.083787076,-0.0048356843,-0.029910477,0.0027225297,0.006907559,-0.015081045,-0.037112895,-0.040289506,0.03544596,-0.039786283,0.02360443,0.027315719,-0.0013062245,-0.016763706,0.022236286,-0.008484071,0.06957095,-0.030570962,0.025884671,-0.02761451,-0.032646768,0.044724185,0.004222378,0.015867336,-0.021339914,0.026356446,-0.014428425,-0.0014693798,0.0032572069,-0.04661128,-0.021214109,3.7741926E-4,-0.013311892,-0.04837257,-0.0073124985,0.015804432,0.017565722,-0.030146364,-0.01783306,0.010064513,0.009781449,0.021387093,0.009734272,0.00504012,-0.039723378,0.0019509824,-0.0054057445,-0.028290719,-0.0065458654,0.021575801,-0.0034262594,-0.031498782,-0.0081852805,-0.026796767,-0.029611686,0.0225508,0.06579676,0.030020557,0.0069311475,-0.0042616925,0.023871768,0.03270967,0.026576607,-0.00332011,0.0451016,0.0024512596,-0.027221365,-0.04409515,-0.0050755027,-0.020742334,0.023840318,0.038905635,-0.03349596,-0.04069838,0.0031884066,-0.057053212,0.02319556,-0.019389914,0.043906443,0.05312176,0.01682661,7.386213E-4,-7.92433E-6,-0.05994676,0.033401605,0.03509999,-0.009718547,-0.015088908,0.009600603,0.020946769,-0.034942735,-0.03233225,0.017895963,-0.01067782,-0.028149188,0.02143427,0.030618139,0.016197577,0.07422579,-6.275578E-4,0.043906443,-0.06944515,-0.04271128,-0.03682983,-0.029454429,0.052838698,-0.03387338,-0.020003222,-0.019688705,-0.011967336,0.0032041324,0.0254758,0.014161086,0.048624184,-0.0037781242,-0.0061998977,0.003872479,0.0073321555,-0.020034673,0.015922375,0.0014143394,-0.008554837,-0.031215718,-0.033024184,0.024422172,0.009938708,-0.03270967,-0.009639917,-0.028337898,-0.018524995,-0.0028109872,0.009506248,-3.2188752E-4,0.04994515,0.0056298375,0.013547779,0.0057006036,-0.0011961439,-0.013311892,-0.010740723,-0.005563003,-0.017880239,-0.019924592,-0.057524987,-0.001509677,0.017188303,0.009246772,0.012824392,0.021638704,-0.012376207,0.039031442,-0.011715723,-0.0017219754,-0.025570156,-0.06799837,0.06875321,-5.8234617E-4,-0.026623784,0.034785476,-0.00837399,-0.017644351,-0.03896854,-0.0028601305,0.008169554,-0.0037034266,0.006455442,-0.008421168,-0.038622573,-0.0011204635,-0.011330441,-0.050605632,-0.017471367,-0.031121364,-0.00151754,0.015010279,-0.00607016,-0.017439915,0.0044621965,0.018273383,0.023510076,0.016244754,-0.039408863,-0.03818225,0.04488144,0.0015214714,0.023730237,-0.03127862,-0.023887495,0.0033436988,-0.031907655,-0.028149188,0.029297171,-0.0035442028,0.0010850804,0.03184475,0.06837579,0.0235258,-0.026246365,0.05117176,0.002740221,0.0027028723,-0.04072983,0.0022645157,-0.035791926,-0.00579889,-0.028212091,0.0140588675,-0.0018261588,-0.0391887,-0.035288703,-0.018933866,0.051863696,0.01824193,-0.0058893133,-0.08057901,0.16128384,-0.027174188,-0.018477818,-0.040195152,-0.026214914,-0.03484838,4.5580257E-4,-0.0031019147,-0.015081045,-0.018965319,-0.00891653,-0.0047649182,-0.028746767,0.039786283,-0.08542256,0.00582641,0.034439508,-0.0040120455,0.0045565516,0.0021662293,0.012564916,-0.027630236,-0.07271611,0.02360443,-0.0020443543,0.005437196,-0.061645146,0.0013337446,0.021072576,-0.02311693,-0.0015077114,0.0040179426,0.03503709,0.117943525,0.027897574,-0.0142004,-0.008924393,-0.040509667,-0.034439508,-0.018194754,-0.057116117,-0.02368306,-0.0073478813,0.017377011,0.025051204,9.1013085E-4,-0.008790724,0.020333463,-0.014813706,-0.011574191,-0.012336892,-0.013728626,0.05956934,-0.008421168,-0.04151612,0.025114108,-6.206778E-4,0.0050440514,0.004297076,0.022692334,-0.08196288,0.05249273,0.016763706,0.03368467,0.010976611,-0.008743546,-0.0028385075,0.0064475792,0.024233462,-0.051014505,-0.03308709,0.023588704,-0.03387338,-0.015780844,0.041295957,-0.009820764,0.0017681699,-0.011668546,0.008609877,0.02918709,-0.034219347,-0.010284675,0.0012904987,0.0064672367,-0.015458465,-0.02124556,-0.013099594,-0.00664022,-0.007925805,-0.009639917,1.085449E-4,-0.01704677,-8.084046E-4,0.0048042326,0.0029171365,0.005189515,-0.014113908,-0.01582802,-0.0033535275,-0.0037643642,0.0012895159,0.008413305,-0.015442738,0.047177408,-0.013241126,0.019783061,-0.01937419,-0.010669957,-0.008028022,0.009207457,0.052964505,-0.06353224,-0.038276605,0.015379835,-0.0033083158,0.013264715,0.033433057,0.15222578,-0.0091288285,0.0022487899,0.010764312,-0.020616528,-0.116874166,0.027897574,-0.021041125,0.03682983,0.04793225,-0.05721047,0.005232761,-0.0098443525,-0.030618139,0.012863707,-0.0451016,0.030854026,-0.019059673,-0.0664887,0.026309269,0.0015607859,0.02439072,-0.044629827,-0.023289913,-0.016095359,-0.016795158,-0.00921532,0.01948427,-0.019783061,0.0043992936,0.0040041828,-0.008460482,0.028841123,-0.02173306,-0.048907246,0.040572572,-0.059695147,-0.03874838,0.021214109,-0.015167537,0.051612087,0.023431446,-0.0095298365,0.006365019,0.016197577,0.01899677,0.05205241,0.015104634,-0.0017907758,0.040037893,-0.09051772,-0.013830843,0.01693669,0.0060033253,0.030508058,-0.006750301,-0.019342737,-0.014326206,0.008028022,-0.07013708,-0.028180638,-0.013783666,-0.04639112,-0.012777215,0.005240624,0.016166124,-0.060198374,-0.005110886,0.012612094,-0.019893141,0.034376606,-0.01604032,-0.010221772,0.021104028,-0.007823587,-0.09366288,0.004411088,-0.016048182,0.024988301,0.01828911,-0.014939513,-0.05796531,0.015411287,0.044126604,-0.022016125,-0.027284268,0.004344253,-0.0035324085,0.024343543,0.03981773,0.024312092,-0.052178215,0.06944515,0.018776609,-0.010363304,-0.0037309467,0.008932256,0.021874592,-0.044629827,-0.04566773,0.04403225,-5.7939754E-4,-0.009828627,0.06054434,-0.05076289,0.01618185,-0.045919344,0.037490316,0.032033462,0.04151612,0.023352817,0.012226812,0.01184153,-0.016323384,-0.019279834,-0.006498688,0.025397172,-0.02056935,0.015568545,0.008987296,-0.014507053,0.01547419,-0.03352741,4.8995705E-4,-0.034187894,-0.019610077,-0.014507053,-0.032017734,0.010103828,-0.0133905215,0.012564916,0.02465806,0.058437083,0.0049064504,-0.01832056,-0.014695763,0.014003827,0.026466526,0.034533862,-0.03780483,-0.021308463,-0.010819352,0.039251603,-0.002482711,-0.0010693546,-0.01330403,-0.004568346,0.0036326605,0.03094838,-0.0019549138,-0.006306047,0.0118179405,9.248738E-4,0.023635881,-0.02468951,0.008405441,-0.0017701357,0.012368344,-0.017377011,0.032552414,0.004454334,-0.0013995965,-0.009694957,-0.0055276197,-0.03777338,-0.04428386,0.03852822,-0.02338427,-0.011644957,-0.0259633,0.0084997965,0.006526208,0.06491611,0.003449848,0.079446755,-0.048907246,0.019154027,-0.027221365,-0.0077410266,0.021151206,0.018304834,-0.030366525,0.0045762085,0.019499995,-0.016590722,-0.03852822,-0.027205639,-0.008523385,-0.0075130025,0.020679431,-0.013579231,0.019248383,0.02019193,-0.014255441,-0.01888669,-0.02379314,8.397333E-5,-0.053687893,0.0013750249,0.06397257,-0.022770962,0.0149159245,0.038056444,-0.02270806,0.010607054,0.002183921,0.028007654,-0.06189676,0.0081852805,0.010693546,-0.013555642,-1.8772678E-4,0.01287157,-0.03509999,-0.0635637,0.013587094,0.01493165,-0.014208263,-0.02360443,-0.00813024,-1.4693796E-4,0.0074736876,-0.07856611,-0.017031044,-0.021528624,0.06428708,-0.030791122,0.039660476,0.003929485,0.028793946,-0.028007654,-0.04013225,-0.037395958,-0.015332658,0.051045958,-0.0149159245,-0.05566934,-0.015977416,7.799015E-4,0.033181444,-0.037710477,-0.026765317,0.025412897,-0.03664112,0.020726608,-0.04094999,-0.014271166,0.0013003274,-0.01227399,0.0318133,-0.030350799,0.044346765,0.039471764,0.0069822567,-0.016810883,-0.053184666,-0.0049182447,-0.0069586677,0.015749391,0.025177011,0.027567333,-0.021890318,0.028605236,-0.031750396,0.008774998,0.04176773,-0.015568545,0.07950966,0.007493345,-0.03091693,-0.008476208,-0.04192499,-0.009883667,0.039471764,0.046328217,-0.015796568,0.017266931,0.10888546,-0.010111691,-0.037112895,-0.03173467,-0.038087893,-0.041012894,-0.0065537286,-0.014452013,0.035917733,0.027095558,-0.033118542,-0.015945964,-0.012557054,-0.02476814,0.062022567,0.05739918,-0.051014505,-0.031750396,0.03994354,-0.030791122,0.027064107,-0.012321167,-0.13335481,-0.017109673,0.04598225,-0.040069345,0.0010614917,0.018194754,-0.029014107,-0.019861689,-0.0056377,0.008853627,-0.046076603,-0.04054112,0.0025436487,-0.024988301,-0.03151451,-0.072024174,0.008594152,-0.015191126,-0.036169346,0.01802177,0.10372739,-0.009207457,-0.0070884055,0.0113697555,0.0030449086,-1.692981E-4,0.02338427,-0.025051204,-0.038716927,-0.036955636,0.0083189495,0.012517739,0.02566451,-0.0146878995,-0.051234666,0.03270967,0.027645962,-0.026309269,0.03736451,-0.018477818,0.009687095,0.0013799393,-0.018823786,0.013241126,-0.03154596,-0.019956045,-0.012785078,-0.0052642124,-0.025790317,-0.020034673,-0.027174188,0.00788649,0.022660881,1.247007E-4,-0.038465314,-0.018697979,0.030586686,0.016008867,-0.0050165313,0.056738697,0.010701409,-0.023777414,0.021402817,-0.02802338,0.012108868,-0.0118651185,-0.038937088,-0.017754432,-0.010355441,-2.0136399E-4,-0.02465806,-0.010717135,0.013060279,0.07604998,-0.026419349,-0.018572174,0.021151206,-0.09510966,0.027331445,0.022660881,-0.0024237393,0.0042774184,0.0039137593,-0.017487092,0.0059718736,0.0032434468,-0.06724353,-0.04761773,0.048341118,0.008295361,-0.037930638,-0.012454836,-0.02967459,-0.009034473,-0.05604676,0.029171364,0.0045290315,0.012730038,0.023368543,0.010371167,0.022157656,0.007988708,-0.023337092,-0.031986285,0.0119987875,-0.021937495,-0.003493094,0.024154833,-0.06724353,0.06806128,0.03368467,0.028793946,0.019043947,-0.02357298,-0.033967733,-0.050637085,-0.075861275,-0.051737893,-0.041673377,0.012203223,0.009341127,0.040572572,0.029800396,-0.03981773,0.007308567,-0.017974593,0.019468544,-0.04598225,-0.0075247968,0.037867732,0.022881044,0.020978222,-0.020113302,0.060481437,-0.004015977,0.015765117,-0.01626048,-0.056707244,-0.023871768,0.01997177,-0.026183462,-0.0010772175,0.055795148,-0.027032655,0.0026596263,-0.053593535,0.017675802,0.042019345,-0.013264715,-2.4034063E-5,0.042396765,-0.03566612,-0.031262897,0.011519151,0.015985278,0.013995965,-0.005390019,0.0038213702,0.028432252,-0.01487661,0.034282252,0.0028955135,-0.021968946,0.018619351,0.021670157,0.009191732,-0.021088302,-0.015544957,0.036106445,0.0064318534,0.0055512083,0.01607177,-0.056864504,0.059191924,0.032961283,0.013272578,0.02479959,-0.019327011,-0.021764511,0.0080752,-0.03799354,-0.04714596,0.005912902,-0.0032237896,-0.033401605,-0.009867941,0.0046627005,-0.010552014,-0.0054136077,-0.04208225,-0.035697572,0.020915318,-0.0010211943,0.029831849,0.014231851,0.030287897,-0.024107656,-0.017282657,-0.07535805,-0.023462897,0.027315719,0.013728626,-0.024422172,-0.011078828,0.006565523,-0.032583863,-0.009726409,-9.4944536E-4,0.015387698,0.027787494,-0.0067463694,-0.005653426,-0.016024593,-0.032127816,0.014766529,0.016685076,-0.010292538,0.010363304,0.009207457,0.0036837694,0.031750396,0.0033161787,0.010630643,-8.487019E-4,0.008232458,0.027299995,-0.016920963,0.045195956,-0.01577298,0.017675802,4.4720253E-4,0.034974184,0.03541451,0.02536572,-0.01444415,-0.019956045,0.0391887,-0.015214714,0.06730644,0.008153829,0.011181046,-0.011857255,-0.0017671871,-0.028180638,-0.029344348,-0.005397882,-0.049976602,-0.031310074,0.019877415,-0.0313258,-0.017298384,0.064538695,0.021811688,0.016669352,0.005936491,-0.034942735,0.010599191,-0.012352618,-0.0109215705,0.015269754,-0.0019421367,-0.0031943037,0.03403064,-0.009734272,-0.024044752,-0.02154435,0.011731449,-0.00940403,0.003917691,0.047334667,0.05538628,0.04566773,0.040226605,0.024170559,0.04403225,0.008586288,-0.022597978,-0.01832056,-0.04425241,0.035005637,0.021512898,0.0027795357,-0.049630634,0.046957247,-0.016244754,-0.06054434,-0.004894656,0.010662095,-0.038276605,-0.017487092,0.04793225,-0.059412085,0.00252006,0.013319755,0.00813024,-0.050825793,-0.043560475,-0.016905239,0.020585077,0.02214193]')) ON CONFLICT (id) DO UPDATE SET content = ('1.没有“灵根”真的就无法修仙吗?
在凡人的世界中，“灵根”与修仙是画上等号的，但事无绝对，在灵界有天材地宝可以凭空生出“灵根”来，灵界的一位人族大能就是用灵宝获得“灵根”成为的修士。但在人界是没有这种灵草的，所以可以说人界没有“灵根”就无法修仙。后面剧情中师妹与韩立关于“灵根”的一番话也是凡人中的一段金典，让人唏嘘不已。

2.“灵根”有几种?韩立的“灵根”怎么样?
灵根普遍上可分为五种，“灵根”分为金、木、水、火、土等五行属性，大部分人的灵根，都是这五种或四种多重属性混杂，这些人虽然也可以感应到天地灵气，但是修炼的效果可以说是惨不忍睹，基本上只能把炼气阶段的五行基本功法，练至三、四层，就寸步不前了，一般一生都无望跨过筑基期。所以具有五种、甚至四种属性的灵根，也被修仙界称为“伪灵根”，以示和只有两、三种属性，修炼起来较为快速的“真灵根”相区别。 至于只有一种属性的单一灵根，则被修仙界称为“天根”，意思是上天的宠儿。因为拥有这种灵根、不论是何属性的人，他修炼的速度都是普通灵根人的二至三倍。而且修炼到筑基期顶峰时，不需面对跨入结丹期时所应面对的瓶颈，可轻易的开始结丹。而在五种基本属性的“灵根”之外还有变异“灵根”的存在，像“金灵根”和“水灵根”异变产生的“雷灵根”；“土灵根”和“水灵根”异变后产生的“冰灵根”，当然还有“暗灵根”、“风灵根”等其他变异灵根。变异“灵根”没有能直接突破结丹期的妙用的，但在修炼速度上与“天灵根”相差无几，所以同样是门派拉拢的对象。而在灵界之中更是有“隐灵根”的存在，其表现在灵根会在') , metadata = ('{"source":"新建 文本文档.txt","knowledge":"《凡人修仙传》设定"}')::jsonb , embedding = ('[0.026403623,-0.06667741,0.03509999,-4.7717983E-4,0.028652413,0.02802338,-0.015466327,0.075106435,0.047869343,0.0568016,0.002726461,0.02479959,-0.024736688,-0.04481854,0.062840305,0.008090925,0.006663809,-0.052366924,0.025979027,-0.037238702,0.040509667,-0.032049187,0.05126612,0.05343628,-0.021780238,0.002978074,0.023557253,0.08051611,-0.019940319,-0.0013878021,-0.05934918,0.014632859,-0.023148382,-0.0027952616,-1.2961501E-4,0.007336087,-0.0053860876,0.017754432,0.07208708,0.026136285,0.004937902,-0.038308054,0.013988102,5.2337436E-4,-0.043183055,0.008460482,0.0025711688,-4.3073957E-4,-0.010465522,-0.011794352,0.03874838,0.0095298365,-0.02461088,0.0017878272,0.02027056,0.025900397,0.008381853,0.0146878995,-0.004536894,0.01238407,0.004725604,0.007693849,0.009679232,0.012391932,-0.019437093,-0.014310481,-0.05148628,0.009805039,0.019499995,-0.055763695,-0.051140312,-0.005905039,-0.0021446063,-0.0035756545,0.028463703,-0.021795962,0.018603625,0.014145359,-0.052178215,-0.0033338703,-0.010795764,-0.009938708,0.0085627,-0.029501606,0.015324795,0.057021763,0.010701409,0.010842941,0.020915318,0.10831933,0.0121324565,-0.022362093,0.023730237,-0.083787076,-0.0048356843,-0.029910477,0.0027225297,0.006907559,-0.015081045,-0.037112895,-0.040289506,0.03544596,-0.039786283,0.02360443,0.027315719,-0.0013062245,-0.016763706,0.022236286,-0.008484071,0.06957095,-0.030570962,0.025884671,-0.02761451,-0.032646768,0.044724185,0.004222378,0.015867336,-0.021339914,0.026356446,-0.014428425,-0.0014693798,0.0032572069,-0.04661128,-0.021214109,3.7741926E-4,-0.013311892,-0.04837257,-0.0073124985,0.015804432,0.017565722,-0.030146364,-0.01783306,0.010064513,0.009781449,0.021387093,0.009734272,0.00504012,-0.039723378,0.0019509824,-0.0054057445,-0.028290719,-0.0065458654,0.021575801,-0.0034262594,-0.031498782,-0.0081852805,-0.026796767,-0.029611686,0.0225508,0.06579676,0.030020557,0.0069311475,-0.0042616925,0.023871768,0.03270967,0.026576607,-0.00332011,0.0451016,0.0024512596,-0.027221365,-0.04409515,-0.0050755027,-0.020742334,0.023840318,0.038905635,-0.03349596,-0.04069838,0.0031884066,-0.057053212,0.02319556,-0.019389914,0.043906443,0.05312176,0.01682661,7.386213E-4,-7.92433E-6,-0.05994676,0.033401605,0.03509999,-0.009718547,-0.015088908,0.009600603,0.020946769,-0.034942735,-0.03233225,0.017895963,-0.01067782,-0.028149188,0.02143427,0.030618139,0.016197577,0.07422579,-6.275578E-4,0.043906443,-0.06944515,-0.04271128,-0.03682983,-0.029454429,0.052838698,-0.03387338,-0.020003222,-0.019688705,-0.011967336,0.0032041324,0.0254758,0.014161086,0.048624184,-0.0037781242,-0.0061998977,0.003872479,0.0073321555,-0.020034673,0.015922375,0.0014143394,-0.008554837,-0.031215718,-0.033024184,0.024422172,0.009938708,-0.03270967,-0.009639917,-0.028337898,-0.018524995,-0.0028109872,0.009506248,-3.2188752E-4,0.04994515,0.0056298375,0.013547779,0.0057006036,-0.0011961439,-0.013311892,-0.010740723,-0.005563003,-0.017880239,-0.019924592,-0.057524987,-0.001509677,0.017188303,0.009246772,0.012824392,0.021638704,-0.012376207,0.039031442,-0.011715723,-0.0017219754,-0.025570156,-0.06799837,0.06875321,-5.8234617E-4,-0.026623784,0.034785476,-0.00837399,-0.017644351,-0.03896854,-0.0028601305,0.008169554,-0.0037034266,0.006455442,-0.008421168,-0.038622573,-0.0011204635,-0.011330441,-0.050605632,-0.017471367,-0.031121364,-0.00151754,0.015010279,-0.00607016,-0.017439915,0.0044621965,0.018273383,0.023510076,0.016244754,-0.039408863,-0.03818225,0.04488144,0.0015214714,0.023730237,-0.03127862,-0.023887495,0.0033436988,-0.031907655,-0.028149188,0.029297171,-0.0035442028,0.0010850804,0.03184475,0.06837579,0.0235258,-0.026246365,0.05117176,0.002740221,0.0027028723,-0.04072983,0.0022645157,-0.035791926,-0.00579889,-0.028212091,0.0140588675,-0.0018261588,-0.0391887,-0.035288703,-0.018933866,0.051863696,0.01824193,-0.0058893133,-0.08057901,0.16128384,-0.027174188,-0.018477818,-0.040195152,-0.026214914,-0.03484838,4.5580257E-4,-0.0031019147,-0.015081045,-0.018965319,-0.00891653,-0.0047649182,-0.028746767,0.039786283,-0.08542256,0.00582641,0.034439508,-0.0040120455,0.0045565516,0.0021662293,0.012564916,-0.027630236,-0.07271611,0.02360443,-0.0020443543,0.005437196,-0.061645146,0.0013337446,0.021072576,-0.02311693,-0.0015077114,0.0040179426,0.03503709,0.117943525,0.027897574,-0.0142004,-0.008924393,-0.040509667,-0.034439508,-0.018194754,-0.057116117,-0.02368306,-0.0073478813,0.017377011,0.025051204,9.1013085E-4,-0.008790724,0.020333463,-0.014813706,-0.011574191,-0.012336892,-0.013728626,0.05956934,-0.008421168,-0.04151612,0.025114108,-6.206778E-4,0.0050440514,0.004297076,0.022692334,-0.08196288,0.05249273,0.016763706,0.03368467,0.010976611,-0.008743546,-0.0028385075,0.0064475792,0.024233462,-0.051014505,-0.03308709,0.023588704,-0.03387338,-0.015780844,0.041295957,-0.009820764,0.0017681699,-0.011668546,0.008609877,0.02918709,-0.034219347,-0.010284675,0.0012904987,0.0064672367,-0.015458465,-0.02124556,-0.013099594,-0.00664022,-0.007925805,-0.009639917,1.085449E-4,-0.01704677,-8.084046E-4,0.0048042326,0.0029171365,0.005189515,-0.014113908,-0.01582802,-0.0033535275,-0.0037643642,0.0012895159,0.008413305,-0.015442738,0.047177408,-0.013241126,0.019783061,-0.01937419,-0.010669957,-0.008028022,0.009207457,0.052964505,-0.06353224,-0.038276605,0.015379835,-0.0033083158,0.013264715,0.033433057,0.15222578,-0.0091288285,0.0022487899,0.010764312,-0.020616528,-0.116874166,0.027897574,-0.021041125,0.03682983,0.04793225,-0.05721047,0.005232761,-0.0098443525,-0.030618139,0.012863707,-0.0451016,0.030854026,-0.019059673,-0.0664887,0.026309269,0.0015607859,0.02439072,-0.044629827,-0.023289913,-0.016095359,-0.016795158,-0.00921532,0.01948427,-0.019783061,0.0043992936,0.0040041828,-0.008460482,0.028841123,-0.02173306,-0.048907246,0.040572572,-0.059695147,-0.03874838,0.021214109,-0.015167537,0.051612087,0.023431446,-0.0095298365,0.006365019,0.016197577,0.01899677,0.05205241,0.015104634,-0.0017907758,0.040037893,-0.09051772,-0.013830843,0.01693669,0.0060033253,0.030508058,-0.006750301,-0.019342737,-0.014326206,0.008028022,-0.07013708,-0.028180638,-0.013783666,-0.04639112,-0.012777215,0.005240624,0.016166124,-0.060198374,-0.005110886,0.012612094,-0.019893141,0.034376606,-0.01604032,-0.010221772,0.021104028,-0.007823587,-0.09366288,0.004411088,-0.016048182,0.024988301,0.01828911,-0.014939513,-0.05796531,0.015411287,0.044126604,-0.022016125,-0.027284268,0.004344253,-0.0035324085,0.024343543,0.03981773,0.024312092,-0.052178215,0.06944515,0.018776609,-0.010363304,-0.0037309467,0.008932256,0.021874592,-0.044629827,-0.04566773,0.04403225,-5.7939754E-4,-0.009828627,0.06054434,-0.05076289,0.01618185,-0.045919344,0.037490316,0.032033462,0.04151612,0.023352817,0.012226812,0.01184153,-0.016323384,-0.019279834,-0.006498688,0.025397172,-0.02056935,0.015568545,0.008987296,-0.014507053,0.01547419,-0.03352741,4.8995705E-4,-0.034187894,-0.019610077,-0.014507053,-0.032017734,0.010103828,-0.0133905215,0.012564916,0.02465806,0.058437083,0.0049064504,-0.01832056,-0.014695763,0.014003827,0.026466526,0.034533862,-0.03780483,-0.021308463,-0.010819352,0.039251603,-0.002482711,-0.0010693546,-0.01330403,-0.004568346,0.0036326605,0.03094838,-0.0019549138,-0.006306047,0.0118179405,9.248738E-4,0.023635881,-0.02468951,0.008405441,-0.0017701357,0.012368344,-0.017377011,0.032552414,0.004454334,-0.0013995965,-0.009694957,-0.0055276197,-0.03777338,-0.04428386,0.03852822,-0.02338427,-0.011644957,-0.0259633,0.0084997965,0.006526208,0.06491611,0.003449848,0.079446755,-0.048907246,0.019154027,-0.027221365,-0.0077410266,0.021151206,0.018304834,-0.030366525,0.0045762085,0.019499995,-0.016590722,-0.03852822,-0.027205639,-0.008523385,-0.0075130025,0.020679431,-0.013579231,0.019248383,0.02019193,-0.014255441,-0.01888669,-0.02379314,8.397333E-5,-0.053687893,0.0013750249,0.06397257,-0.022770962,0.0149159245,0.038056444,-0.02270806,0.010607054,0.002183921,0.028007654,-0.06189676,0.0081852805,0.010693546,-0.013555642,-1.8772678E-4,0.01287157,-0.03509999,-0.0635637,0.013587094,0.01493165,-0.014208263,-0.02360443,-0.00813024,-1.4693796E-4,0.0074736876,-0.07856611,-0.017031044,-0.021528624,0.06428708,-0.030791122,0.039660476,0.003929485,0.028793946,-0.028007654,-0.04013225,-0.037395958,-0.015332658,0.051045958,-0.0149159245,-0.05566934,-0.015977416,7.799015E-4,0.033181444,-0.037710477,-0.026765317,0.025412897,-0.03664112,0.020726608,-0.04094999,-0.014271166,0.0013003274,-0.01227399,0.0318133,-0.030350799,0.044346765,0.039471764,0.0069822567,-0.016810883,-0.053184666,-0.0049182447,-0.0069586677,0.015749391,0.025177011,0.027567333,-0.021890318,0.028605236,-0.031750396,0.008774998,0.04176773,-0.015568545,0.07950966,0.007493345,-0.03091693,-0.008476208,-0.04192499,-0.009883667,0.039471764,0.046328217,-0.015796568,0.017266931,0.10888546,-0.010111691,-0.037112895,-0.03173467,-0.038087893,-0.041012894,-0.0065537286,-0.014452013,0.035917733,0.027095558,-0.033118542,-0.015945964,-0.012557054,-0.02476814,0.062022567,0.05739918,-0.051014505,-0.031750396,0.03994354,-0.030791122,0.027064107,-0.012321167,-0.13335481,-0.017109673,0.04598225,-0.040069345,0.0010614917,0.018194754,-0.029014107,-0.019861689,-0.0056377,0.008853627,-0.046076603,-0.04054112,0.0025436487,-0.024988301,-0.03151451,-0.072024174,0.008594152,-0.015191126,-0.036169346,0.01802177,0.10372739,-0.009207457,-0.0070884055,0.0113697555,0.0030449086,-1.692981E-4,0.02338427,-0.025051204,-0.038716927,-0.036955636,0.0083189495,0.012517739,0.02566451,-0.0146878995,-0.051234666,0.03270967,0.027645962,-0.026309269,0.03736451,-0.018477818,0.009687095,0.0013799393,-0.018823786,0.013241126,-0.03154596,-0.019956045,-0.012785078,-0.0052642124,-0.025790317,-0.020034673,-0.027174188,0.00788649,0.022660881,1.247007E-4,-0.038465314,-0.018697979,0.030586686,0.016008867,-0.0050165313,0.056738697,0.010701409,-0.023777414,0.021402817,-0.02802338,0.012108868,-0.0118651185,-0.038937088,-0.017754432,-0.010355441,-2.0136399E-4,-0.02465806,-0.010717135,0.013060279,0.07604998,-0.026419349,-0.018572174,0.021151206,-0.09510966,0.027331445,0.022660881,-0.0024237393,0.0042774184,0.0039137593,-0.017487092,0.0059718736,0.0032434468,-0.06724353,-0.04761773,0.048341118,0.008295361,-0.037930638,-0.012454836,-0.02967459,-0.009034473,-0.05604676,0.029171364,0.0045290315,0.012730038,0.023368543,0.010371167,0.022157656,0.007988708,-0.023337092,-0.031986285,0.0119987875,-0.021937495,-0.003493094,0.024154833,-0.06724353,0.06806128,0.03368467,0.028793946,0.019043947,-0.02357298,-0.033967733,-0.050637085,-0.075861275,-0.051737893,-0.041673377,0.012203223,0.009341127,0.040572572,0.029800396,-0.03981773,0.007308567,-0.017974593,0.019468544,-0.04598225,-0.0075247968,0.037867732,0.022881044,0.020978222,-0.020113302,0.060481437,-0.004015977,0.015765117,-0.01626048,-0.056707244,-0.023871768,0.01997177,-0.026183462,-0.0010772175,0.055795148,-0.027032655,0.0026596263,-0.053593535,0.017675802,0.042019345,-0.013264715,-2.4034063E-5,0.042396765,-0.03566612,-0.031262897,0.011519151,0.015985278,0.013995965,-0.005390019,0.0038213702,0.028432252,-0.01487661,0.034282252,0.0028955135,-0.021968946,0.018619351,0.021670157,0.009191732,-0.021088302,-0.015544957,0.036106445,0.0064318534,0.0055512083,0.01607177,-0.056864504,0.059191924,0.032961283,0.013272578,0.02479959,-0.019327011,-0.021764511,0.0080752,-0.03799354,-0.04714596,0.005912902,-0.0032237896,-0.033401605,-0.009867941,0.0046627005,-0.010552014,-0.0054136077,-0.04208225,-0.035697572,0.020915318,-0.0010211943,0.029831849,0.014231851,0.030287897,-0.024107656,-0.017282657,-0.07535805,-0.023462897,0.027315719,0.013728626,-0.024422172,-0.011078828,0.006565523,-0.032583863,-0.009726409,-9.4944536E-4,0.015387698,0.027787494,-0.0067463694,-0.005653426,-0.016024593,-0.032127816,0.014766529,0.016685076,-0.010292538,0.010363304,0.009207457,0.0036837694,0.031750396,0.0033161787,0.010630643,-8.487019E-4,0.008232458,0.027299995,-0.016920963,0.045195956,-0.01577298,0.017675802,4.4720253E-4,0.034974184,0.03541451,0.02536572,-0.01444415,-0.019956045,0.0391887,-0.015214714,0.06730644,0.008153829,0.011181046,-0.011857255,-0.0017671871,-0.028180638,-0.029344348,-0.005397882,-0.049976602,-0.031310074,0.019877415,-0.0313258,-0.017298384,0.064538695,0.021811688,0.016669352,0.005936491,-0.034942735,0.010599191,-0.012352618,-0.0109215705,0.015269754,-0.0019421367,-0.0031943037,0.03403064,-0.009734272,-0.024044752,-0.02154435,0.011731449,-0.00940403,0.003917691,0.047334667,0.05538628,0.04566773,0.040226605,0.024170559,0.04403225,0.008586288,-0.022597978,-0.01832056,-0.04425241,0.035005637,0.021512898,0.0027795357,-0.049630634,0.046957247,-0.016244754,-0.06054434,-0.004894656,0.010662095,-0.038276605,-0.017487092,0.04793225,-0.059412085,0.00252006,0.013319755,0.00813024,-0.050825793,-0.043560475,-0.016905239,0.020585077,0.02214193]')  was aborted: ERROR: expected 1536 dimensions, not 1024  Call getNextException to see other errors in the batch.] with root cause
org.postgresql.util.PSQLException: ERROR: expected 1536 dimensions, not 1024
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2733)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2420)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:580)
	at org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:889)
	at org.postgresql.jdbc.PgStatement.executeBatch(PgStatement.java:913)
	at org.postgresql.jdbc.PgPreparedStatement.executeBatch(PgPreparedStatement.java:1739)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate.lambda$getPreparedStatementCallback$7(JdbcTemplate.java:1617)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:701)
	at org.springframework.jdbc.core.JdbcTemplate.batchUpdate(JdbcTemplate.java:1049)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.insertOrUpdateBatch(PgVectorStore.java:274)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.lambda$doAdd$0(PgVectorStore.java:258)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.springframework.ai.vectorstore.pgvector.PgVectorStore.doAdd(PgVectorStore.java:258)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.lambda$add$1(AbstractObservationVectorStore.java:86)
	at io.micrometer.observation.Observation.observe(Observation.java:498)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.add(AbstractObservationVectorStore.java:86)
	at org.springframework.ai.vectorstore.VectorStore.accept(VectorStore.java:56)
	at cn.bugstack.xfg.dev.tech.trigger.http.RagTagController.lambda$uploadRagTag$1(RagTagController.java:63)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at cn.bugstack.xfg.dev.tech.trigger.http.RagTagController.uploadRagTag(RagTagController.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
25-07-30.09:21:24.429 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-07-30.09:21:24.444 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-07-30.09:21:24.461 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown initiated...
25-07-30.09:21:24.461 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown completed.
25-07-30.09:21:28.878 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 31032 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-30.09:21:28.881 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-30.09:21:30.304 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-30.09:21:30.309 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-30.09:21:30.362 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
25-07-30.09:21:31.552 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-30.09:21:31.575 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-30.09:21:31.578 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-30.09:21:31.578 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-07-30.09:21:31.876 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-30.09:21:31.876 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2683 ms
25-07-30.09:21:32.637 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-07-30.09:21:32.648 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-07-30.09:21:32.648 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:21:33.385 [main            ] INFO  Version                - Redisson 3.44.0
25-07-30.09:21:33.788 [redisson-netty-1-5] INFO  ConnectionsHolder      - 1 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:21:33.806 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:21:33.968 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama. Is empty: false
25-07-30.09:21:33.970 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama in schema: public
25-07-30.09:21:33.970 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:21:34.749 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-30.09:21:34.763 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path '/'
25-07-30.09:21:34.772 [main            ] INFO  Application            - Started Application in 6.784 seconds (process running for 8.289)
25-07-30.09:21:48.621 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-30.09:21:48.623 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-30.09:21:48.624 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 1 ms
25-07-30.09:21:48.735 [http-nio-8080-exec-1] INFO  RagTagController       - 上传知识库开始-ragTag:《凡人修仙传》设定
25-07-30.09:21:49.819 [http-nio-8080-exec-1] INFO  TextSplitter           - Splitting up document into 3 chunks.
25-07-30.09:21:50.917 [http-nio-8080-exec-1] INFO  HikariDataSource       - HikariCP - Starting...
25-07-30.09:21:51.115 [http-nio-8080-exec-1] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@166bf919
25-07-30.09:21:51.118 [http-nio-8080-exec-1] INFO  HikariDataSource       - HikariCP - Start completed.
25-07-30.09:21:51.206 [http-nio-8080-exec-1] INFO  RagTagController       - 上传知识库完成-ragTag:《凡人修仙传》设定
25-07-30.09:25:18.398 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-07-30.09:25:18.412 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-07-30.09:25:18.429 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown initiated...
25-07-30.09:25:18.432 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown completed.
25-07-30.09:25:22.619 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 19924 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-30.09:25:22.620 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-30.09:25:24.086 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-30.09:25:24.091 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-30.09:25:24.141 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
25-07-30.09:25:25.333 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-30.09:25:25.356 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-30.09:25:25.357 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-30.09:25:25.357 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-07-30.09:25:25.646 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-30.09:25:25.646 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2730 ms
25-07-30.09:25:26.417 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_openai. Is empty: false
25-07-30.09:25:26.426 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_openai in schema: public
25-07-30.09:25:26.426 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:25:27.166 [main            ] INFO  Version                - Redisson 3.44.0
25-07-30.09:25:27.541 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:25:27.556 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 192.168.100.150/192.168.100.150:26379
25-07-30.09:25:27.721 [main            ] INFO  PgVectorStore          - Using the vector table name: vector_store_ollama. Is empty: false
25-07-30.09:25:27.722 [main            ] INFO  PgVectorStore          - Initializing PGVectorStore schema for table: vector_store_ollama in schema: public
25-07-30.09:25:27.722 [main            ] INFO  PgVectorStore          - vectorTableValidationsEnabled false
25-07-30.09:25:28.524 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-30.09:25:28.537 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path '/'
25-07-30.09:25:28.546 [main            ] INFO  Application            - Started Application in 6.8 seconds (process running for 8.228)
25-07-30.09:26:20.264 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-30.09:26:20.264 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-30.09:26:20.266 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 2 ms
25-07-30.09:29:59.589 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-07-30.09:29:59.604 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
