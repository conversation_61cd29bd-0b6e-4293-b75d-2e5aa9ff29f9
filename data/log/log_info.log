25-07-29.09:58:55.132 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 26540 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-29.09:58:55.134 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-29.09:58:55.997 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-29.09:58:56.002 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-29.09:58:56.050 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
25-07-29.09:58:57.011 [main            ] INFO  TomcatWebServer        - Tom<PERSON> initialized with port 8080 (http)
25-07-29.09:58:57.023 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-29.09:58:57.025 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-29.09:58:57.026 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-29.09:58:57.164 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-29.09:58:57.164 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1978 ms
25-07-29.09:58:57.767 [main            ] INFO  HikariDataSource       - HikariCP - Starting...
25-07-29.09:58:57.934 [main            ] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@1e8633c7
25-07-29.09:58:57.936 [main            ] INFO  HikariDataSource       - HikariCP - Start completed.
25-07-29.09:59:02.895 [main            ] INFO  Version                - Redisson 3.44.0
25-07-29.09:59:03.206 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-29.09:59:03.226 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-29.09:59:03.873 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-29.09:59:03.883 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path ''
25-07-29.09:59:03.890 [main            ] INFO  Application            - Started Application in 9.578 seconds (process running for 10.922)
25-07-29.09:59:20.966 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-29.09:59:20.966 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-29.09:59:20.967 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 1 ms
