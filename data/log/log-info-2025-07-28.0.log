25-07-28.15:11:05.352 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 32252 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.15:11:05.353 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.15:11:06.296 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.15:11:06.299 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.15:11:06.334 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
25-07-28.15:11:07.238 [main            ] INFO  TomcatWebServer        - <PERSON><PERSON> initialized with port 8090 (http)
25-07-28.15:11:07.251 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8090"]
25-07-28.15:11:07.253 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.15:11:07.253 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.15:11:07.384 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.15:11:07.385 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1779 ms
25-07-28.15:11:08.246 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.15:11:31.587 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonClient' defined in class path resource [cn/bugstack/xfg/dev/tech/config/RedisClientConfig.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
25-07-28.15:11:31.589 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-07-28.15:11:31.617 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-07-28.15:11:31.634 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonClient' defined in class path resource [cn/bugstack/xfg/dev/tech/config/RedisClientConfig.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:639)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at cn.bugstack.xfg.dev.tech.Application.main(Application.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 19 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.redisson.connection.MasterSlaveConnectionManager.doConnect(MasterSlaveConnectionManager.java:227)
	at org.redisson.connection.MasterSlaveConnectionManager.connect(MasterSlaveConnectionManager.java:187)
	at org.redisson.connection.ConnectionManager.create(ConnectionManager.java:98)
	at org.redisson.Redisson.<init>(Redisson.java:67)
	at org.redisson.Redisson.create(Redisson.java:110)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig.redissonClient(RedisClientConfig.java:39)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig$$SpringCGLIB$$0.CGLIB$redissonClient$0(<generated>)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig$$SpringCGLIB$$0.redissonClient(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 20 common frames omitted
Caused by: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.redisson.connection.MasterSlaveConnectionManager.doConnect(MasterSlaveConnectionManager.java:222)
	... 33 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$2(ConnectionsHolder.java:167)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2194)
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$5(ConnectionsHolder.java:181)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2194)
	at org.redisson.client.RedisClient$2$2.run(RedisClient.java:325)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.ConnectTimeoutException: connection timed out after 5000 ms: 192.168.1.109/192.168.1.109:16379
	at java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:368)
	at java.base/java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:377)
	at java.base/java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:1097)
	... 11 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out after 5000 ms: 192.168.1.109/192.168.1.109:16379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	... 8 common frames omitted
25-07-28.15:13:46.569 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 30208 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.15:13:46.571 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.15:13:47.386 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.15:13:47.398 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.15:13:47.427 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
25-07-28.15:13:48.127 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8090 (http)
25-07-28.15:13:48.139 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8090"]
25-07-28.15:13:48.140 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.15:13:48.140 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.15:13:48.269 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.15:13:48.269 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1476 ms
25-07-28.15:13:49.010 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.15:14:12.354 [main            ] WARN  AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonClient' defined in class path resource [cn/bugstack/xfg/dev/tech/config/RedisClientConfig.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
25-07-28.15:14:12.356 [main            ] INFO  StandardService        - Stopping service [Tomcat]
25-07-28.15:14:12.370 [main            ] INFO  ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
25-07-28.15:14:12.389 [main            ] ERROR SpringApplication      - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redissonClient' defined in class path resource [cn/bugstack/xfg/dev/tech/config/RedisClientConfig.class]: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:639)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at cn.bugstack.xfg.dev.tech.Application.main(Application.java:12)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'redissonClient' threw exception with message: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 19 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.redisson.connection.MasterSlaveConnectionManager.doConnect(MasterSlaveConnectionManager.java:227)
	at org.redisson.connection.MasterSlaveConnectionManager.connect(MasterSlaveConnectionManager.java:187)
	at org.redisson.connection.ConnectionManager.create(ConnectionManager.java:98)
	at org.redisson.Redisson.<init>(Redisson.java:67)
	at org.redisson.Redisson.create(Redisson.java:110)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig.redissonClient(RedisClientConfig.java:39)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig$$SpringCGLIB$$0.CGLIB$redissonClient$0(<generated>)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at cn.bugstack.xfg.dev.tech.config.RedisClientConfig$$SpringCGLIB$$0.redissonClient(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 20 common frames omitted
Caused by: java.util.concurrent.ExecutionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at org.redisson.connection.MasterSlaveConnectionManager.doConnect(MasterSlaveConnectionManager.java:222)
	... 33 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 192.168.1.109/192.168.1.109:16379
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$2(ConnectionsHolder.java:167)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2194)
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$5(ConnectionsHolder.java:181)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2194)
	at org.redisson.client.RedisClient$2$2.run(RedisClient.java:325)
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:166)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.ConnectTimeoutException: connection timed out after 5000 ms: 192.168.1.109/192.168.1.109:16379
	at java.base/java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:368)
	at java.base/java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:377)
	at java.base/java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:1097)
	... 11 common frames omitted
Caused by: io.netty.channel.ConnectTimeoutException: connection timed out after 5000 ms: 192.168.1.109/192.168.1.109:16379
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe$1.run(AbstractNioChannel.java:261)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:153)
	... 8 common frames omitted
25-07-28.15:18:42.108 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 36164 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.15:18:42.113 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.15:18:42.674 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.15:18:42.675 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.15:18:42.703 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
25-07-28.15:18:43.134 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-28.15:18:43.145 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-28.15:18:43.146 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.15:18:43.146 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.15:18:43.225 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.15:18:43.225 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1069 ms
25-07-28.15:18:43.752 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.15:18:44.008 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-28.15:18:44.028 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-28.15:18:44.723 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-28.15:18:44.735 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path ''
25-07-28.15:18:44.741 [main            ] INFO  Application            - Started Application in 3.267 seconds (process running for 3.9)
25-07-28.15:18:54.592 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-28.15:18:54.593 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-28.15:18:54.593 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 0 ms
25-07-28.15:36:54.596 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 37776 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.15:36:54.598 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.15:36:55.142 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.15:36:55.145 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.15:36:55.172 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
25-07-28.15:36:55.625 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-28.15:36:55.638 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-28.15:36:55.639 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.15:36:55.639 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.15:36:55.722 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.15:36:55.722 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1079 ms
25-07-28.15:36:56.304 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.15:36:56.557 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-28.15:36:56.579 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-28.15:36:57.253 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-28.15:36:57.262 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path ''
25-07-28.15:36:57.268 [main            ] INFO  Application            - Started Application in 3.294 seconds (process running for 4.045)
25-07-28.15:37:00.437 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-28.15:37:00.437 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-28.15:37:00.437 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 0 ms
25-07-28.15:37:00.604 [http-nio-8080-exec-1] WARN  OllamaApi              - [404] Not Found - {"error":"model \"deppseek-r1:1.5b\" not found, try pulling it first"}
25-07-28.15:37:00.606 [http-nio-8080-exec-1] ERROR [dispatcherServlet]    - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.RuntimeException: [404] Not Found - {"error":"model \"deppseek-r1:1.5b\" not found, try pulling it first"}] with root cause
java.lang.RuntimeException: [404] Not Found - {"error":"model \"deppseek-r1:1.5b\" not found, try pulling it first"}
	at org.springframework.ai.ollama.api.OllamaApi$OllamaResponseErrorHandler.handleError(OllamaApi.java:78)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.StatusHandler.lambda$fromErrorHandler$1(StatusHandler.java:71)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:680)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:667)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:613)
	at org.springframework.ai.ollama.api.OllamaApi.chat(OllamaApi.java:520)
	at org.springframework.ai.ollama.OllamaChatClient.call(OllamaChatClient.java:85)
	at cn.bugstack.xfg.dev.tech.trigger.http.OllamaController.generate(OllamaController.java:33)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:259)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:192)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:920)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
25-07-28.15:53:57.425 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 32244 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.15:53:57.426 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.15:53:57.993 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.15:53:57.996 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.15:53:58.026 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
25-07-28.15:53:58.465 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-28.15:53:58.475 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-28.15:53:58.477 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.15:53:58.477 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.15:53:58.557 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.15:53:58.559 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1083 ms
25-07-28.15:53:59.086 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.15:53:59.331 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-28.15:53:59.347 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-28.15:53:59.951 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-28.15:53:59.959 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path ''
25-07-28.15:53:59.966 [main            ] INFO  Application            - Started Application in 3.125 seconds (process running for 3.843)
25-07-28.15:54:13.420 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-28.15:54:13.422 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-28.15:54:13.422 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 0 ms
25-07-28.15:56:47.652 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 11744 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.15:56:47.653 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.15:56:48.235 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.15:56:48.236 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.15:56:48.269 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
25-07-28.15:56:48.746 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-28.15:56:48.756 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-28.15:56:48.758 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.15:56:48.758 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.15:56:48.843 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.15:56:48.844 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1149 ms
25-07-28.15:56:49.403 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.15:56:49.655 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-28.15:56:49.673 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-28.15:56:50.297 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-28.15:56:50.308 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path ''
25-07-28.15:56:50.315 [main            ] INFO  Application            - Started Application in 3.221 seconds (process running for 3.885)
25-07-28.15:57:18.353 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-28.15:57:18.353 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-28.15:57:18.353 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 0 ms
25-07-28.18:01:38.977 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 37024 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.18:01:38.978 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.18:01:39.648 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.18:01:39.650 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.18:01:39.681 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
25-07-28.18:01:40.397 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8090 (http)
25-07-28.18:01:40.408 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8090"]
25-07-28.18:01:40.409 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.18:01:40.409 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.18:01:40.533 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.18:01:40.533 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1511 ms
25-07-28.18:01:41.014 [main            ] INFO  HikariDataSource       - HikariCP - Starting...
25-07-28.18:01:41.143 [main            ] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@e3b9817
25-07-28.18:01:41.144 [main            ] INFO  HikariDataSource       - HikariCP - Start completed.
25-07-28.18:01:41.737 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.18:01:41.980 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-28.18:01:41.997 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-28.18:01:42.536 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8090"]
25-07-28.18:01:42.545 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8090 (http) with context path ''
25-07-28.18:01:42.552 [main            ] INFO  Application            - Started Application in 4.208 seconds (process running for 5.04)
25-07-28.18:03:06.584 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown initiated...
25-07-28.18:03:06.586 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown completed.
25-07-28.18:03:09.968 [main            ] INFO  Application            - Starting Application using Java 21.0.6 with PID 34236 (D:\ACode\project\ai-rag-knowledge\xfg-dev-tech-app\target\classes started by 30562 in D:\ACode\project\ai-rag-knowledge)
25-07-28.18:03:09.970 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-07-28.18:03:10.677 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-07-28.18:03:10.681 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-07-28.18:03:10.711 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
25-07-28.18:03:11.415 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8080 (http)
25-07-28.18:03:11.426 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8080"]
25-07-28.18:03:11.427 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-07-28.18:03:11.427 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-07-28.18:03:11.550 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-07-28.18:03:11.550 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1535 ms
25-07-28.18:03:11.994 [main            ] INFO  HikariDataSource       - HikariCP - Starting...
25-07-28.18:03:12.115 [main            ] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@75d7e17c
25-07-28.18:03:12.117 [main            ] INFO  HikariDataSource       - HikariCP - Start completed.
25-07-28.18:03:12.690 [main            ] INFO  Version                - Redisson 3.44.0
25-07-28.18:03:12.937 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for ***************/***************:26379
25-07-28.18:03:12.955 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for ***************/***************:26379
25-07-28.18:03:13.513 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8080"]
25-07-28.18:03:13.523 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8080 (http) with context path ''
25-07-28.18:03:13.531 [main            ] INFO  Application            - Started Application in 4.207 seconds (process running for 5.05)
25-07-28.18:03:30.732 [http-nio-8080-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-07-28.18:03:30.732 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-07-28.18:03:30.733 [http-nio-8080-exec-1] INFO  DispatcherServlet      - Completed initialization in 1 ms
25-07-28.18:46:51.710 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown initiated...
25-07-28.18:46:51.711 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown completed.
