package cn.bugstack.xfg.dev.tech.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/29
 */
@Configuration
public class OpenAiConfig {

    @Bean
    public OpenAiApi openAiApi(@Value("${spring.ai.openai.api-key}") String apiKey,
                               @Value("${spring.ai.openai.base-url}") String baseUrl) {
        return OpenAiApi.builder().apiKey(apiKey).baseUrl(baseUrl).build();
    }

    @Bean("openAiChatClient")
    public ChatClient openAiChatClient(OpenAiChatModel openAiChatModel) {
        return ChatClient.create(openAiChatModel);
    }

    @Bean("openAiSimpleVectorStore")
    public SimpleVectorStore openAiSimpleVectorStore(OpenAiEmbeddingModel openAiEmbeddingModel) {
        //OpenAiEmbeddingModel openAiEmbeddingModel = new OpenAiEmbeddingModel(openAiApi);
        return SimpleVectorStore.builder(openAiEmbeddingModel)
                .build();
    }

    @Bean("openAiPgVectorStore")
    public PgVectorStore openAiPgVectorStore(JdbcTemplate jdbcTemplate, OpenAiEmbeddingModel openAiEmbeddingModel) {
        //OpenAiEmbeddingModel openAiEmbeddingModel = new OpenAiEmbeddingModel(openAiApi);
        return PgVectorStore.builder(jdbcTemplate, openAiEmbeddingModel)
                .vectorTableName("vector_store_openai")
                .dimensions(1536)
                .build();
    }

}
