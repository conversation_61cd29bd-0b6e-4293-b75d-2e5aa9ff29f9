package cn.bugstack.xfg.dev.tech.config;



import org.springframework.ai.chat.client.ChatClient;

import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.OllamaEmbeddingModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/28
 */
@Configuration
public class OllamaConfig {

    @Bean
    public OllamaApi ollamaApi(@Value("${spring.ai.ollama.base-url}") String baseUrl) {
        return new OllamaApi(baseUrl);
    }

    @Bean
    public OllamaChatModel ollamaChatModel(OllamaApi ollamaApi) {
        return OllamaChatModel.builder()
                .ollamaApi(ollamaApi)
                .build();
    }
    @Bean("ollamaChatClient")
    public ChatClient ollamaChatClient(OllamaChatModel ollamaChatModel) {
        return ChatClient.create(ollamaChatModel);
    }

    @Bean
    public TokenTextSplitter tokenTextSplitter() {
        return new TokenTextSplitter();
    }

    @Bean("ollamaSimpleVectorStore")
    public SimpleVectorStore ollamaSimpleVectorStore(OllamaApi ollamaApi) {
        OllamaEmbeddingModel ollamaEmbeddingModel = OllamaEmbeddingModel.builder()
                .ollamaApi(ollamaApi)
                .defaultOptions(OllamaOptions.builder()
                        .model("nomic-embed-text")
                        .build())
                .build();
        return SimpleVectorStore.builder(ollamaEmbeddingModel)
                .build();
    }

    @Bean("ollamaPgVectorStore")
    public PgVectorStore ollamaPgVectorStore(OllamaApi ollamaApi, JdbcTemplate jdbcTemplate) {
        OllamaEmbeddingModel ollamaEmbeddingModel = OllamaEmbeddingModel.builder()
                .ollamaApi(ollamaApi)
                .defaultOptions(OllamaOptions.builder()
                        .model("nomic-embed-text")
                        .build())
                .build();
        return PgVectorStore.builder(jdbcTemplate, ollamaEmbeddingModel)
                .vectorTableName("vector_store_ollama")
                .build();
    }

}
