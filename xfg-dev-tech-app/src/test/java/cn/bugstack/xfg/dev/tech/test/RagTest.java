package cn.bugstack.xfg.dev.tech.test;

import com.alibaba.fastjson2.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.ai.chat.ChatResponse;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.ollama.OllamaChatClient;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.PgVectorStore;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/28
 */
@SpringBootTest
@Slf4j
@RunWith(SpringRunner.class)
public class RagTest {
    @Resource
    private TokenTextSplitter tokenTextSplitter;
    @Resource
    private OllamaChatClient chatClient;
    @Resource
    private SimpleVectorStore simpleVectorStore;
    @Resource
    private PgVectorStore pgVectorStore;

    @Value("${spring.ai.ollama.model}")
    private String model;

    @Test
    public void upload() {
        TikaDocumentReader tikaDocumentReader = new TikaDocumentReader("knowledge.text");
        List<Document> documents = tikaDocumentReader.get();
        List<Document> splitDocuments = tokenTextSplitter.apply(documents);

        splitDocuments.forEach(doc -> doc.getMetadata().put("knowledge", "外部知识库"));

        pgVectorStore.accept(splitDocuments);
        log.info("上传成功");
    }

    @Test
    public void query() {
        String prompt = "用户想了解 {message}。请先提炼问题的核心意思，然后结合以下知识库中的内容，给出最清晰、最准确的答案。DOCUMENTS:{documents}";
        String message = "张三的绰号是什么";

        String query = SearchRequest.query(message)
                .withTopK(5)
                .withFilterExpression("knowledge == '外部知识库'")
                .getQuery();

        List<Document> documents = pgVectorStore.similaritySearch(query);
        String document = documents.stream()
                .map(Document::getContent)
                .collect(Collectors.joining());

        Message finalMessage = new SystemPromptTemplate(prompt).createMessage(Map.of("message", message, "documents", document));
        log.info("最终的prompt：{}", JSON.toJSONString(finalMessage));

        ChatResponse chatResponse = chatClient.call(new Prompt(finalMessage, OllamaOptions.create().withModel(model)));
        log.info("结果：{}", JSON.toJSONString(chatResponse));
    }



}
