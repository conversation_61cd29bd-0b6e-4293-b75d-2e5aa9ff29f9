package cn.bugstack.xfg.dev.tech.api;

import cn.bugstack.xfg.dev.tech.api.response.ApiResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description:
 * @Author: flyxy
 * @Date: 2025/7/29
 */
public interface IRagTagService {

    ApiResponse<List<String>> queryRagTagList();

    ApiResponse<String> uploadRagTag(String ragTag, List<MultipartFile> files);
}
