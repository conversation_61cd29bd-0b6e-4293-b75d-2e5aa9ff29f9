<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型接口测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">模型接口测试</h1>
        
        <div class="mb-4">
            <label for="modelSelect" class="block text-sm font-medium text-gray-700 mb-2">选择模型</label>
            <select id="modelSelect" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
                <option value="local-model">本地模型</option>
                <option value="deepseek-r1">DeepSeek-R1</option>
                <option value="deepseek-v3" selected>DeepSeek-V3</option>
                <option value="qwen-plus">Qwen-Plus</option>
                <option value="qwen-turbo">Qwen-Turbo</option>
            </select>
        </div>

        <div class="mb-4">
            <label for="messageInput" class="block text-sm font-medium text-gray-700 mb-2">测试消息</label>
            <input
                type="text"
                id="messageInput"
                placeholder="输入测试消息..."
                value="你好，请介绍一下自己"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
        </div>

        <button
            id="testButton"
            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
            测试接口
        </button>

        <div id="result" class="mt-6 hidden">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">接口信息</h3>
            <div class="bg-gray-100 p-4 rounded-md mb-4">
                <p><strong>选择的模型:</strong> <span id="selectedModel"></span></p>
                <p><strong>实际模型参数:</strong> <span id="actualModel"></span></p>
                <p><strong>接口地址:</strong> <span id="apiUrl" class="break-all"></span></p>
            </div>
            
            <h3 class="text-lg font-semibold text-gray-800 mb-2">响应结果</h3>
            <div id="response" class="bg-gray-100 p-4 rounded-md max-h-64 overflow-y-auto">
                <!-- Response will be displayed here -->
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testButton').addEventListener('click', async function() {
            const selectedModel = document.getElementById('modelSelect').value;
            const message = document.getElementById('messageInput').value.trim();
            
            if (!message) {
                alert('请输入测试消息');
                return;
            }
            
            // 根据选择的模型确定接口地址和模型参数
            let apiUrl, model;
            
            if (selectedModel === 'local-model') {
                // 本地模型使用 ollama 接口
                model = 'deepseek-r1:1.5b';
                apiUrl = `http://127.0.0.1:8080/api/v1/ollama/generate_stream?model=${encodeURIComponent(model)}&message=${encodeURIComponent(message)}`;
            } else {
                // 其他模型使用 openai 接口
                model = selectedModel;
                apiUrl = `http://127.0.0.1:8080/api/v1/openai/generate_stream?model=${encodeURIComponent(model)}&message=${encodeURIComponent(message)}`;
            }
            
            // 显示接口信息
            document.getElementById('selectedModel').textContent = selectedModel;
            document.getElementById('actualModel').textContent = model;
            document.getElementById('apiUrl').textContent = apiUrl;
            document.getElementById('result').classList.remove('hidden');
            
            const testButton = this;
            const responseDiv = document.getElementById('response');
            
            testButton.disabled = true;
            testButton.textContent = '测试中...';
            responseDiv.innerHTML = '<p class="text-gray-600">正在连接...</p>';
            
            try {
                const eventSource = new EventSource(apiUrl);
                let responseContent = '';
                let hasContent = false;
                
                eventSource.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        console.log('Received data:', data);
                        
                        // 根据不同接口的响应格式处理
                        let content = '';
                        if (data.result && data.result.output && data.result.output.content) {
                            // ollama 接口格式
                            content = data.result.output.content;
                        } else if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                            // openai 接口格式
                            content = data.choices[0].delta.content;
                        }
                        
                        if (content && content.trim()) {
                            responseContent += content;
                            hasContent = true;
                            responseDiv.innerHTML = `<pre class="whitespace-pre-wrap">${responseContent}</pre>`;
                        }
                        
                        // 检查是否结束
                        const isFinished = (data.result && data.result.metadata && data.result.metadata.finishReason) ||
                                         (data.choices && data.choices[0] && data.choices[0].finish_reason);
                        
                        if (isFinished) {
                            eventSource.close();
                            if (!hasContent) {
                                responseDiv.innerHTML = '<p class="text-gray-600">没有收到响应内容</p>';
                            }
                        }
                        
                    } catch (error) {
                        console.error('解析响应数据失败:', error);
                        responseDiv.innerHTML += `<p class="text-red-600">解析错误: ${error.message}</p>`;
                    }
                };
                
                eventSource.onerror = (error) => {
                    console.error('EventSource错误:', error);
                    eventSource.close();
                    if (!hasContent) {
                        responseDiv.innerHTML = '<p class="text-red-600">连接失败或超时</p>';
                    }
                };
                
                // 设置超时
                setTimeout(() => {
                    if (eventSource.readyState !== EventSource.CLOSED) {
                        eventSource.close();
                        if (!hasContent) {
                            responseDiv.innerHTML = '<p class="text-yellow-600">请求超时</p>';
                        }
                    }
                }, 30000);
                
            } catch (error) {
                console.error('请求失败:', error);
                responseDiv.innerHTML = `<p class="text-red-600">请求失败: ${error.message}</p>`;
            } finally {
                testButton.disabled = false;
                testButton.textContent = '测试接口';
            }
        });
    </script>
</body>
</html>
