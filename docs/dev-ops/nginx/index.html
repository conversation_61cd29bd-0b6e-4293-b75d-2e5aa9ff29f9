<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        .typing-indicator:nth-child(3) { animation-delay: 0s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .scroll-smooth {
            scroll-behavior: smooth;
        }

        .chat-item:hover .chat-actions {
            opacity: 1;
        }

        .chat-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }

        .sidebar-collapsed {
            width: 60px;
        }

        .sidebar-expanded {
            width: 280px;
        }

        .sidebar-transition {
            transition: width 0.3s ease-in-out;
        }

        .drag-over {
            border-color: #3b82f6 !important;
            background-color: #eff6ff !important;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
        }

        .file-item:hover {
            background-color: #f3f4f6;
        }

        .file-remove {
            color: #ef4444;
            cursor: pointer;
            padding: 2px;
            border-radius: 4px;
        }

        .file-remove:hover {
            background-color: #fee2e2;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="bg-white border-r border-gray-200 sidebar-expanded sidebar-transition flex flex-col">
            <!-- Sidebar Header -->
            <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                <h2 id="sidebarTitle" class="text-lg font-semibold text-gray-800">聊天列表</h2>
                <button id="toggleSidebar" class="p-1 hover:bg-gray-100 rounded">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                    </svg>
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="p-4 space-y-3">
                <button id="newChatButton" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span id="newChatText">新建聊天</span>
                </button>

                <button id="uploadButton" class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span id="uploadText">上传文件</span>
                </button>
            </div>

            <!-- Chat List -->
            <div id="chatList" class="flex-1 overflow-y-auto">
                <!-- Chat items will be dynamically added here -->
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="flex-1 flex flex-col">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 p-4">
                <h1 id="chatTitle" class="text-2xl font-bold text-gray-800 text-center">AI 对话助手</h1>
            </header>

            <!-- Chat Messages Container -->
            <div id="chatContainer" class="flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth">
                <div class="text-center text-gray-500 text-sm">
                    开始与AI助手对话吧！
                </div>
            </div>

            <!-- Input Area -->
            <div class="bg-white border-t border-gray-200 p-4">
                <div class="flex space-x-3">
                    <input
                        type="text"
                        id="messageInput"
                        placeholder="输入您的问题..."
                        class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        maxlength="1000"
                    >
                    <button
                        id="sendButton"
                        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        发送
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">上传知识库文件</h3>
                <button id="closeUploadModal" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <!-- Knowledge Base Name Input -->
                <div class="mb-4">
                    <label for="ragTagInput" class="block text-sm font-medium text-gray-700 mb-2">知识库名称</label>
                    <input
                        type="text"
                        id="ragTagInput"
                        placeholder="请输入知识库名称"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        required
                    >
                </div>

                <!-- File Upload Area -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                    <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">
                                <span class="font-medium text-blue-600 hover:text-blue-500">点击选择文件</span>
                                或拖拽文件到此处
                            </p>
                            <p class="text-xs text-gray-500 mt-1">支持 PDF, DOC, DOCX, TXT 等格式</p>
                        </div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt,.md" class="hidden">
                </div>

                <!-- Selected Files List -->
                <div id="fileList" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">已选择的文件</label>
                    <div id="selectedFiles" class="space-y-2 max-h-32 overflow-y-auto">
                        <!-- Selected files will be displayed here -->
                    </div>
                </div>

                <!-- Upload Progress -->
                <div id="uploadProgress" class="mb-4 hidden">
                    <div class="flex items-center justify-between text-sm text-gray-600 mb-1">
                        <span>上传进度</span>
                        <span id="progressText">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
                <button id="cancelUpload" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                    取消
                </button>
                <button id="startUpload" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                    开始上传
                </button>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.chatContainer = document.getElementById('chatContainer');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.sidebar = document.getElementById('sidebar');
                this.toggleSidebar = document.getElementById('toggleSidebar');
                this.newChatButton = document.getElementById('newChatButton');
                this.uploadButton = document.getElementById('uploadButton');
                this.chatList = document.getElementById('chatList');
                this.chatTitle = document.getElementById('chatTitle');
                this.sidebarTitle = document.getElementById('sidebarTitle');
                this.newChatText = document.getElementById('newChatText');
                this.uploadText = document.getElementById('uploadText');

                // Upload modal elements
                this.uploadModal = document.getElementById('uploadModal');
                this.closeUploadModal = document.getElementById('closeUploadModal');
                this.cancelUpload = document.getElementById('cancelUpload');
                this.startUpload = document.getElementById('startUpload');
                this.ragTagInput = document.getElementById('ragTagInput');
                this.dropZone = document.getElementById('dropZone');
                this.fileInput = document.getElementById('fileInput');
                this.fileList = document.getElementById('fileList');
                this.selectedFiles = document.getElementById('selectedFiles');
                this.uploadProgress = document.getElementById('uploadProgress');
                this.progressBar = document.getElementById('progressBar');
                this.progressText = document.getElementById('progressText');

                this.isLoading = false;
                this.isSidebarCollapsed = false;
                this.chats = this.loadChats();
                this.currentChatId = null;
                this.chatIdCounter = this.loadChatIdCounter();
                this.selectedFilesList = [];

                this.init();
            }

            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                this.toggleSidebar.addEventListener('click', () => this.toggleSidebarCollapse());
                this.newChatButton.addEventListener('click', () => this.createNewChat());
                this.uploadButton.addEventListener('click', () => this.openUploadModal());

                // Upload modal events
                this.closeUploadModal.addEventListener('click', () => this.closeUploadModalHandler());
                this.cancelUpload.addEventListener('click', () => this.closeUploadModalHandler());
                this.startUpload.addEventListener('click', () => this.handleUpload());

                // File input and drag & drop events
                this.setupFileUpload();

                // 初始化聊天列表
                this.renderChatList();

                // 如果没有聊天，创建第一个
                if (Object.keys(this.chats).length === 0) {
                    this.createNewChat();
                } else {
                    // 选择最后一个聊天
                    const chatIds = Object.keys(this.chats);
                    this.selectChat(chatIds[chatIds.length - 1]);
                }
            }

            // 聊天管理方法
            loadChats() {
                const saved = localStorage.getItem('chatApp_chats');
                return saved ? JSON.parse(saved) : {};
            }

            saveChats() {
                localStorage.setItem('chatApp_chats', JSON.stringify(this.chats));
            }

            loadChatIdCounter() {
                const saved = localStorage.getItem('chatApp_chatIdCounter');
                return saved ? parseInt(saved) : 1;
            }

            saveChatIdCounter() {
                localStorage.setItem('chatApp_chatIdCounter', this.chatIdCounter.toString());
            }

            createNewChat() {
                const chatId = `chat_${this.chatIdCounter++}`;
                const chatName = `聊天 ${this.chatIdCounter - 1}`;

                this.chats[chatId] = {
                    id: chatId,
                    name: chatName,
                    messages: [],
                    createdAt: new Date().toISOString()
                };

                this.saveChats();
                this.saveChatIdCounter();
                this.renderChatList();
                this.selectChat(chatId);
            }

            selectChat(chatId) {
                if (!this.chats[chatId]) return;

                this.currentChatId = chatId;
                this.chatTitle.textContent = this.chats[chatId].name;
                this.renderMessages();
                this.updateChatListSelection();
            }

            deleteChat(chatId) {
                if (!this.chats[chatId]) return;

                if (Object.keys(this.chats).length === 1) {
                    alert('至少需要保留一个聊天');
                    return;
                }

                if (confirm(`确定要删除聊天"${this.chats[chatId].name}"吗？`)) {
                    delete this.chats[chatId];
                    this.saveChats();

                    // 如果删除的是当前聊天，切换到其他聊天
                    if (this.currentChatId === chatId) {
                        const remainingChatIds = Object.keys(this.chats);
                        if (remainingChatIds.length > 0) {
                            this.selectChat(remainingChatIds[0]);
                        }
                    }

                    this.renderChatList();
                }
            }

            renameChat(chatId) {
                if (!this.chats[chatId]) return;

                const currentName = this.chats[chatId].name;
                const newName = prompt('请输入新的聊天名称:', currentName);

                if (newName && newName.trim() && newName.trim() !== currentName) {
                    this.chats[chatId].name = newName.trim();
                    this.saveChats();
                    this.renderChatList();

                    if (this.currentChatId === chatId) {
                        this.chatTitle.textContent = newName.trim();
                    }
                }
            }

            toggleSidebarCollapse() {
                this.isSidebarCollapsed = !this.isSidebarCollapsed;

                if (this.isSidebarCollapsed) {
                    this.sidebar.classList.remove('sidebar-expanded');
                    this.sidebar.classList.add('sidebar-collapsed');
                    this.sidebarTitle.style.display = 'none';
                    this.newChatText.style.display = 'none';
                    this.uploadText.style.display = 'none';
                } else {
                    this.sidebar.classList.remove('sidebar-collapsed');
                    this.sidebar.classList.add('sidebar-expanded');
                    this.sidebarTitle.style.display = 'block';
                    this.newChatText.style.display = 'inline';
                    this.uploadText.style.display = 'inline';
                }
            }

            // 文件上传相关方法
            openUploadModal() {
                this.uploadModal.classList.remove('hidden');
                this.resetUploadModal();
            }

            closeUploadModalHandler() {
                this.uploadModal.classList.add('hidden');
                this.resetUploadModal();
            }

            resetUploadModal() {
                this.ragTagInput.value = '';
                this.selectedFilesList = [];
                this.fileList.classList.add('hidden');
                this.selectedFiles.innerHTML = '';
                this.uploadProgress.classList.add('hidden');
                this.progressBar.style.width = '0%';
                this.progressText.textContent = '0%';
                this.startUpload.disabled = false;
                this.fileInput.value = '';
            }

            setupFileUpload() {
                // File input change event
                this.fileInput.addEventListener('change', (e) => {
                    this.handleFileSelect(Array.from(e.target.files));
                });

                // Drop zone click event
                this.dropZone.addEventListener('click', () => {
                    this.fileInput.click();
                });

                // Drag and drop events
                this.dropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.dropZone.classList.add('drag-over');
                });

                this.dropZone.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    this.dropZone.classList.remove('drag-over');
                });

                this.dropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.dropZone.classList.remove('drag-over');
                    const files = Array.from(e.dataTransfer.files);
                    this.handleFileSelect(files);
                });
            }

            handleFileSelect(files) {
                // Filter supported file types
                const supportedTypes = ['.pdf', '.doc', '.docx', '.txt', '.md'];
                const validFiles = files.filter(file => {
                    const extension = '.' + file.name.split('.').pop().toLowerCase();
                    return supportedTypes.includes(extension);
                });

                if (validFiles.length !== files.length) {
                    alert('部分文件格式不支持，仅支持 PDF, DOC, DOCX, TXT, MD 格式');
                }

                // Add valid files to selection
                validFiles.forEach(file => {
                    if (!this.selectedFilesList.find(f => f.name === file.name && f.size === file.size)) {
                        this.selectedFilesList.push(file);
                    }
                });

                this.renderSelectedFiles();
            }

            renderSelectedFiles() {
                if (this.selectedFilesList.length === 0) {
                    this.fileList.classList.add('hidden');
                    return;
                }

                this.fileList.classList.remove('hidden');
                this.selectedFiles.innerHTML = '';

                this.selectedFilesList.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';

                    const fileSize = this.formatFileSize(file.size);
                    fileItem.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <div>
                                <div class="text-sm font-medium text-gray-900">${file.name}</div>
                                <div class="text-xs text-gray-500">${fileSize}</div>
                            </div>
                        </div>
                        <button class="file-remove" data-index="${index}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    `;

                    // Remove file event
                    fileItem.querySelector('.file-remove').addEventListener('click', () => {
                        this.selectedFilesList.splice(index, 1);
                        this.renderSelectedFiles();
                    });

                    this.selectedFiles.appendChild(fileItem);
                });
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            async handleUpload() {
                const ragTag = this.ragTagInput.value.trim();

                if (!ragTag) {
                    alert('请输入知识库名称');
                    return;
                }

                if (this.selectedFilesList.length === 0) {
                    alert('请选择要上传的文件');
                    return;
                }

                this.startUpload.disabled = true;
                this.uploadProgress.classList.remove('hidden');

                try {
                    const formData = new FormData();
                    formData.append('ragTag', ragTag);

                    this.selectedFilesList.forEach(file => {
                        formData.append('files', file);
                    });

                    const response = await fetch('/api/v1/ragTag/file/upload', {
                        method: 'POST',
                        body: formData,
                        onUploadProgress: (progressEvent) => {
                            if (progressEvent.lengthComputable) {
                                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                                this.updateProgress(percentCompleted);
                            }
                        }
                    });

                    // Simulate progress for browsers that don't support upload progress
                    this.simulateProgress();

                    if (response.ok) {
                        const result = await response.json();
                        if (result.code === '0000') {
                            this.updateProgress(100);
                            setTimeout(() => {
                                alert('文件上传成功！');
                                this.closeUploadModalHandler();
                            }, 500);
                        } else {
                            throw new Error(result.message || '上传失败');
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('上传失败:', error);
                    alert(`上传失败: ${error.message}`);
                    this.startUpload.disabled = false;
                    this.uploadProgress.classList.add('hidden');
                }
            }

            simulateProgress() {
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress >= 90) {
                        progress = 90;
                        clearInterval(interval);
                    }
                    this.updateProgress(Math.round(progress));
                }, 200);
            }

            updateProgress(percent) {
                this.progressBar.style.width = `${percent}%`;
                this.progressText.textContent = `${percent}%`;
            }

            renderChatList() {
                this.chatList.innerHTML = '';

                const chatIds = Object.keys(this.chats).sort((a, b) => {
                    return new Date(this.chats[b].createdAt) - new Date(this.chats[a].createdAt);
                });

                chatIds.forEach(chatId => {
                    const chat = this.chats[chatId];
                    const chatItem = document.createElement('div');
                    chatItem.className = `chat-item p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 relative ${
                        this.currentChatId === chatId ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`;

                    chatItem.innerHTML = `
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 truncate">${chat.name}</div>
                                <div class="text-xs text-gray-500">${this.formatDate(chat.createdAt)}</div>
                            </div>
                            <div class="chat-actions flex space-x-1 ml-2">
                                <button class="rename-btn p-1 hover:bg-gray-200 rounded" title="重命名">
                                    <svg class="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </button>
                                <button class="delete-btn p-1 hover:bg-red-200 rounded" title="删除">
                                    <svg class="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    `;

                    // 点击聊天项切换聊天
                    chatItem.addEventListener('click', (e) => {
                        if (!e.target.closest('.chat-actions')) {
                            this.selectChat(chatId);
                        }
                    });

                    // 重命名按钮
                    chatItem.querySelector('.rename-btn').addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.renameChat(chatId);
                    });

                    // 删除按钮
                    chatItem.querySelector('.delete-btn').addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.deleteChat(chatId);
                    });

                    this.chatList.appendChild(chatItem);
                });
            }

            updateChatListSelection() {
                const chatItems = this.chatList.querySelectorAll('.chat-item');
                chatItems.forEach(item => {
                    item.classList.remove('bg-blue-50', 'border-l-4', 'border-l-blue-500');
                });

                const currentChatItem = Array.from(chatItems).find(item => {
                    const chatName = item.querySelector('.text-sm').textContent;
                    return this.chats[this.currentChatId] && this.chats[this.currentChatId].name === chatName;
                });

                if (currentChatItem) {
                    currentChatItem.classList.add('bg-blue-50', 'border-l-4', 'border-l-blue-500');
                }
            }

            renderMessages() {
                this.chatContainer.innerHTML = '';

                if (!this.currentChatId || !this.chats[this.currentChatId]) {
                    this.chatContainer.innerHTML = '<div class="text-center text-gray-500 text-sm">开始与AI助手对话吧！</div>';
                    return;
                }

                const messages = this.chats[this.currentChatId].messages;
                if (messages.length === 0) {
                    this.chatContainer.innerHTML = '<div class="text-center text-gray-500 text-sm">开始与AI助手对话吧！</div>';
                    return;
                }

                messages.forEach(msg => {
                    this.addMessage(msg.content, msg.isUser);
                });
            }

            formatDate(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffTime = Math.abs(now - date);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays === 1) {
                    return '今天';
                } else if (diffDays === 2) {
                    return '昨天';
                } else if (diffDays <= 7) {
                    return `${diffDays - 1}天前`;
                } else {
                    return date.toLocaleDateString('zh-CN');
                }
            }

            addMessage(content, isUser = false, isTyping = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `flex ${isUser ? 'justify-end' : 'justify-start'}`;

                const messageContent = document.createElement('div');
                messageContent.className = `max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                    isUser
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-800 shadow-sm border border-gray-200'
                }`;

                if (isTyping) {
                    messageContent.innerHTML = `
                        <div class="flex items-center space-x-1">
                            <span class="typing-indicator"></span>
                            <span class="typing-indicator"></span>
                            <span class="typing-indicator"></span>
                        </div>
                    `;
                } else {
                    const contentElement = document.createElement('div');
                    contentElement.className = 'message-content';
                    contentElement.textContent = content;
                    messageContent.appendChild(contentElement);
                }

                messageDiv.appendChild(messageContent);
                this.chatContainer.appendChild(messageDiv);
                this.scrollToBottom();

                return messageDiv;
            }

            scrollToBottom() {
                this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
            }

            setLoading(loading) {
                this.isLoading = loading;
                this.sendButton.disabled = loading;
                this.messageInput.disabled = loading;

                if (loading) {
                    this.sendButton.textContent = '发送中...';
                } else {
                    this.sendButton.textContent = '发送';
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isLoading || !this.currentChatId) return;

                // 保存用户消息到当前聊天
                this.chats[this.currentChatId].messages.push({
                    content: message,
                    isUser: true,
                    timestamp: new Date().toISOString()
                });

                // Add user message to UI
                this.addMessage(message, true);
                this.messageInput.value = '';

                // Add typing indicator
                const typingMessage = this.addMessage('', false, true);

                this.setLoading(true);

                try {
                    const aiResponse = await this.callStreamAPI(message, typingMessage);

                    // 保存AI回复到当前聊天
                    if (aiResponse) {
                        this.chats[this.currentChatId].messages.push({
                            content: aiResponse,
                            isUser: false,
                            timestamp: new Date().toISOString()
                        });
                    }
                } catch (error) {
                    console.error('API调用失败:', error);
                    typingMessage.remove();
                    this.addMessage('抱歉，服务暂时不可用，请稍后再试。', false);
                } finally {
                    this.setLoading(false);
                    this.saveChats();
                }
            }

            async callStreamAPI(message, typingMessage) {
                const model = 'deepseek-r1:1.5b';
                const apiUrl = `http://127.0.0.1:8080/api/v1/ollama/generate_stream_rag?model=${encodeURIComponent(model)}&message=${encodeURIComponent(message)}`;

                return new Promise((resolve, reject) => {
                    const eventSource = new EventSource(apiUrl);
                    let responseContent = '';
                    let hasContent = false;
                    let isFirstContent = true;
                    let isCompleted = false;

                    eventSource.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);

                            // 从 result.output.content 获取内容
                            if (data.result && data.result.output && data.result.output.content) {
                                const content = data.result.output.content;
                                if (content && content.trim()) {
                                    // 过滤掉 <think> 标签内容，只保留实际回复
                                    if (!content.includes('<think>') && !content.includes('</think>')) {
                                        responseContent += content;
                                        hasContent = true;

                                        // 第一次收到有效内容时，移除打字指示器并创建消息
                                        if (isFirstContent) {
                                            typingMessage.remove();
                                            typingMessage = this.addMessage(responseContent, false);
                                            isFirstContent = false;
                                        } else {
                                            // 更新消息内容
                                            const messageContentDiv = typingMessage.querySelector('.message-content');
                                            if (messageContentDiv) {
                                                messageContentDiv.textContent = responseContent;
                                            }
                                        }

                                        this.scrollToBottom();
                                    }
                                }
                            }

                            // 检查是否结束 - 检查多个可能的结束标识
                            const finishReason = (data.result && data.result.metadata && data.result.metadata.finishReason) ||
                                                (data.result && data.result.output && data.result.output.properties && data.result.output.properties.finishReason);

                            // 检查是否为结束消息：finishReason不为null且不为空，或者content为空且有finishReason
                            if (finishReason && (finishReason === 'STOP' || finishReason === 'unknown')) {
                                isCompleted = true;
                                eventSource.close();
                                // 如果没有收到任何内容，移除打字指示器并显示默认消息
                                if (!hasContent) {
                                    typingMessage.remove();
                                    this.addMessage('AI助手暂时没有回复内容。', false);
                                    responseContent = 'AI助手暂时没有回复内容。';
                                }
                                resolve(responseContent);
                                return;
                            }

                            // 如果收到空内容且有finishReason，也认为是结束
                            if (data.result && data.result.output &&
                                data.result.output.content === '' &&
                                finishReason) {
                                isCompleted = true;
                                eventSource.close();
                                resolve(responseContent);
                                return;
                            }

                        } catch (error) {
                            console.error('解析响应数据失败:', error);
                        }
                    };

                    eventSource.onerror = (error) => {
                        console.error('EventSource错误:', error);
                        eventSource.close();

                        // 如果已经完成或者已经有内容，则认为是正常结束
                        if (isCompleted || hasContent) {
                            resolve(responseContent);
                        } else {
                            reject(error);
                        }
                    };

                    // 设置超时
                    setTimeout(() => {
                        if (eventSource.readyState !== EventSource.CLOSED && !isCompleted) {
                            eventSource.close();
                            if (!hasContent) {
                                reject(new Error('请求超时'));
                            } else {
                                resolve(responseContent);
                            }
                        }
                    }, 60000); // 60秒超时，给AI更多时间思考
                });
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>
