<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6b7280;
            animation: typing 1.4s infinite ease-in-out;
        }
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        .typing-indicator:nth-child(3) { animation-delay: 0s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        .scroll-smooth {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="max-w-4xl mx-auto h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 p-4">
            <h1 class="text-2xl font-bold text-gray-800 text-center">AI 对话助手</h1>
        </header>

        <!-- Chat Messages Container -->
        <div id="chatContainer" class="flex-1 overflow-y-auto p-4 space-y-4 scroll-smooth">
            <div class="text-center text-gray-500 text-sm">
                开始与AI助手对话吧！
            </div>
        </div>

        <!-- Input Area -->
        <div class="bg-white border-t border-gray-200 p-4">
            <div class="flex space-x-3">
                <input
                    type="text"
                    id="messageInput"
                    placeholder="输入您的问题..."
                    class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    maxlength="1000"
                >
                <button
                    id="sendButton"
                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    发送
                </button>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.chatContainer = document.getElementById('chatContainer');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.isLoading = false;

                this.init();
            }

            init() {
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }

            addMessage(content, isUser = false, isTyping = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `flex ${isUser ? 'justify-end' : 'justify-start'}`;

                const messageContent = document.createElement('div');
                messageContent.className = `max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                    isUser
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-800 shadow-sm border border-gray-200'
                }`;

                if (isTyping) {
                    messageContent.innerHTML = `
                        <div class="flex items-center space-x-1">
                            <span class="typing-indicator"></span>
                            <span class="typing-indicator"></span>
                            <span class="typing-indicator"></span>
                        </div>
                    `;
                } else {
                    const contentElement = document.createElement('div');
                    contentElement.className = 'message-content';
                    contentElement.textContent = content;
                    messageContent.appendChild(contentElement);
                }

                messageDiv.appendChild(messageContent);
                this.chatContainer.appendChild(messageDiv);
                this.scrollToBottom();

                return messageDiv;
            }

            scrollToBottom() {
                this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
            }

            setLoading(loading) {
                this.isLoading = loading;
                this.sendButton.disabled = loading;
                this.messageInput.disabled = loading;

                if (loading) {
                    this.sendButton.textContent = '发送中...';
                } else {
                    this.sendButton.textContent = '发送';
                }
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message || this.isLoading) return;

                // Add user message
                this.addMessage(message, true);
                this.messageInput.value = '';

                // Add typing indicator
                const typingMessage = this.addMessage('', false, true);

                this.setLoading(true);

                try {
                    await this.callStreamAPI(message, typingMessage);
                } catch (error) {
                    console.error('API调用失败:', error);
                    typingMessage.remove();
                    this.addMessage('抱歉，服务暂时不可用，请稍后再试。', false);
                } finally {
                    this.setLoading(false);
                }
            }

            async callStreamAPI(message, typingMessage) {
                const model = 'deepseek-r1:1.5b';
                const apiUrl = `http://127.0.0.1:8080/api/v1/ollama/generate_stream?model=${encodeURIComponent(model)}&message=${encodeURIComponent(message)}`;

                return new Promise((resolve, reject) => {
                    const eventSource = new EventSource(apiUrl);
                    let responseContent = '';
                    let hasContent = false;
                    let isFirstContent = true;
                    let isCompleted = false;

                    eventSource.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);

                            // 从 result.output.content 获取内容
                            if (data.result && data.result.output && data.result.output.content) {
                                const content = data.result.output.content;
                                if (content && content.trim()) {
                                    // 过滤掉 <think> 标签内容，只保留实际回复
                                    if (!content.includes('<think>') && !content.includes('</think>')) {
                                        responseContent += content;
                                        hasContent = true;

                                        // 第一次收到有效内容时，移除打字指示器并创建消息
                                        if (isFirstContent) {
                                            typingMessage.remove();
                                            typingMessage = this.addMessage(responseContent, false);
                                            isFirstContent = false;
                                        } else {
                                            // 更新消息内容
                                            const messageContentDiv = typingMessage.querySelector('.message-content');
                                            if (messageContentDiv) {
                                                messageContentDiv.textContent = responseContent;
                                            }
                                        }

                                        this.scrollToBottom();
                                    }
                                }
                            }

                            // 检查是否结束 - 检查多个可能的结束标识
                            const finishReason = (data.result && data.result.metadata && data.result.metadata.finishReason) ||
                                                (data.result && data.result.output && data.result.output.properties && data.result.output.properties.finishReason);

                            // 检查是否为结束消息：finishReason不为null且不为空，或者content为空且有finishReason
                            if (finishReason && (finishReason === 'STOP' || finishReason === 'unknown')) {
                                isCompleted = true;
                                eventSource.close();
                                // 如果没有收到任何内容，移除打字指示器并显示默认消息
                                if (!hasContent) {
                                    typingMessage.remove();
                                    this.addMessage('AI助手暂时没有回复内容。', false);
                                }
                                resolve();
                                return;
                            }

                            // 如果收到空内容且有finishReason，也认为是结束
                            if (data.result && data.result.output &&
                                data.result.output.content === '' &&
                                finishReason) {
                                isCompleted = true;
                                eventSource.close();
                                resolve();
                                return;
                            }

                        } catch (error) {
                            console.error('解析响应数据失败:', error);
                        }
                    };

                    eventSource.onerror = (error) => {
                        console.error('EventSource错误:', error);
                        eventSource.close();

                        // 如果已经完成或者已经有内容，则认为是正常结束
                        if (isCompleted || hasContent) {
                            resolve();
                        } else {
                            reject(error);
                        }
                    };

                    // 设置超时
                    setTimeout(() => {
                        if (eventSource.readyState !== EventSource.CLOSED && !isCompleted) {
                            eventSource.close();
                            if (!hasContent) {
                                reject(new Error('请求超时'));
                            } else {
                                resolve();
                            }
                        }
                    }, 60000); // 60秒超时，给AI更多时间思考
                });
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>
