<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">文件上传测试</h1>
        
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="mb-4">
                <label for="ragTag" class="block text-sm font-medium text-gray-700 mb-2">知识库名称</label>
                <input
                    type="text"
                    id="ragTag"
                    name="ragTag"
                    placeholder="请输入知识库名称"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                >
            </div>

            <div class="mb-4">
                <label for="files" class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                <input
                    type="file"
                    id="files"
                    name="files"
                    multiple
                    accept=".pdf,.doc,.docx,.txt,.md"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                >
            </div>

            <button
                type="submit"
                class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
                上传文件
            </button>
        </form>

        <div id="result" class="mt-4 hidden">
            <div class="p-4 rounded-md">
                <p id="resultMessage" class="text-sm"></p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const ragTag = document.getElementById('ragTag').value.trim();
            const files = document.getElementById('files').files;
            
            if (!ragTag) {
                alert('请输入知识库名称');
                return;
            }
            
            if (files.length === 0) {
                alert('请选择文件');
                return;
            }
            
            formData.append('ragTag', ragTag);
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }
            
            const submitButton = e.target.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.textContent = '上传中...';
            
            try {
                const response = await fetch('/api/v1/ragTag/file/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('result');
                const resultMessage = document.getElementById('resultMessage');
                
                resultDiv.classList.remove('hidden');
                
                if (response.ok && result.code === '0000') {
                    resultDiv.className = 'mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md';
                    resultMessage.textContent = '文件上传成功！';
                } else {
                    resultDiv.className = 'mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md';
                    resultMessage.textContent = `上传失败: ${result.message || '未知错误'}`;
                }
            } catch (error) {
                const resultDiv = document.getElementById('result');
                const resultMessage = document.getElementById('resultMessage');
                
                resultDiv.classList.remove('hidden');
                resultDiv.className = 'mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md';
                resultMessage.textContent = `上传失败: ${error.message}`;
            } finally {
                submitButton.disabled = false;
                submitButton.textContent = '上传文件';
            }
        });
    </script>
</body>
</html>
